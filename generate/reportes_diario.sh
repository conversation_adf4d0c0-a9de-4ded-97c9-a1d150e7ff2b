#!/bin/bash

if [ -z "$1" ]; then
	    fecha=$(date -d "yesterday" +"%Y/%m/%d")
    else
	        fecha=$1
fi

#Date 01/04 el parametro es 01/04 -1 = 31/3
echo "Inicio de la malla"

sh -x /home/<USER>/generate/prepare.sh "$fecha"

sh -x /home/<USER>/generate/ejecuta_log_user.sh "$fecha"

sh -x /home/<USER>/generate/ejecuta_diario.sh "$fecha"


cd /home/<USER>/generate/rep_interop/

python3 application.py "$fecha" > "/home/<USER>/generate/logs/interope/INTEROPE-NIUBIZ.log" 2>&1 &

echo "Fin de la malla"

#Agregar logica para que si el parametro+1 es el primer dia del mes entrante, recien se ejecute
#sh -x /home/<USER>/generate/ejecuta_mensual.sh "$fecha"

