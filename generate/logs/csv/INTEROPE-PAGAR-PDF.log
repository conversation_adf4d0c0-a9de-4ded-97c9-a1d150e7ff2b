2025-05-12 08:00:11,351 - root - INFO - No se encontraron datos para exportar.
2025-05-12 08:00:11,352 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-FCOMPARTAMOS-PAGAR-202505-PDF.csv
2025-05-12 08:00:18,954 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-05-12 08:00:18,955 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-BNACION-PAGAR-202505-PDF.csv
FCOMPARTAMOS
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCOMPARTAMOS' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCOMPARTAMOS' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCOMPARTAMOS' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCOMPARTAMOS' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

No se encontraron registros en la consulta.
[]
BNACION
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'BNACION' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'BNACION' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'BNACION' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'BNACION' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

[<src.core.models.report_row.ReportRow object at 0x7fd74a81efd0>, <src.core.models.report_row.ReportRow object at 0x7fd74a81ef10>, <src.core.models.report_row.ReportRow object at 0x7fd74a81ef40>, <src.core.models.report_row.ReportRow object at 0x7fd74a860970>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ed250>, <src.core.models.report_row.ReportRow object at 0x7fd74a81eee0>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ed310>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ed370>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ed3d0>]
CRANDES
2025-05-12 08:00:25,488 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-05-12 08:00:25,489 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-CRANDES-PAGAR-202505-PDF.csv
2025-05-12 08:00:31,823 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-05-12 08:00:31,824 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-CCUSCO-PAGAR-202505-PDF.csv
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CRANDES' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CRANDES' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CRANDES' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CRANDES' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

[<src.core.models.report_row.ReportRow object at 0x7fd74a7ee160>, <src.core.models.report_row.ReportRow object at 0x7fd74a860310>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ee280>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ee2e0>, <src.core.models.report_row.ReportRow object at 0x7fd74a7ee340>]
CCUSCO
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CCUSCO' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CCUSCO' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'CCUSCO' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'CCUSCO' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

[<src.core.models.report_row.ReportRow object at 0x7fd74a7f9250>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f9130>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f9370>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f93d0>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f9430>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f9490>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f94f0>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f9550>, <src.core.models.report_row.ReportRow object at 0x7fd74a7f95b0>]
FCONFIANZA
2025-05-12 08:00:38,078 - root - INFO - No se encontraron datos para exportar.
2025-05-12 08:00:38,079 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-0231FCONFIANZA-PAGAR-202505-PDF.csv
2025-05-12 08:00:44,170 - root - INFO - No se encontraron datos para exportar.
2025-05-12 08:00:44,171 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/INTEROPE-FQAPAQ-PAGAR-202505-PDF.csv
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCONFIANZA' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCONFIANZA' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FCONFIANZA' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FCONFIANZA' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

No se encontraron registros en la consulta.
[]
FQAPAQ
SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FQAPAQ' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FQAPAQ' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

Ejecutando query: SELECT
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END AS "Agente",
CASE
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END  AS "Emisor",
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END  AS "Destino",
TLTX."TransactionType",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Cantidad",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -1
ELSE 1
END AS "Comision",
CASE 
WHEN TLTX."TransactionType" = 'REVERSAL' THEN -COUNT(1)
ELSE COUNT(1)
END AS "Total"
FROM USR_DATALAKE.LOG_TRX_FINAL TLTX
INNER JOIN USR_DATALAKE.PRE_LOG_TRX PLT ON TLTX."TransactionID" = PLT."TransferID"
WHERE 1=1
AND TLTX."From_BankDomain" <> TLTX."To_BankDomain"
AND TLTX."TransactionType" IN ('CASH_IN', 'CASH_OUT', 'CASH_OUT_ATM', 'REVERSAL')
AND ((TLTX."From_BankDomain" = 'FQAPAQ' AND PLT."From_Workspace" <> 'BUSINESS') 
OR (TLTX."To_BankDomain" = 'FQAPAQ' AND PLT."To_Workspace" <> 'BUSINESS'))
AND TRUNC("TransferDate",'MM') = TRUNC(TO_DATE('2025-05-11 00:00:00','YYYY-MM-DD HH24:MI:SS'),'MM')
GROUP BY 
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."FromID" || ' ' || TLTX."FromMSISDN"
ELSE TLTX."ToID" || ' ' || TLTX."ToMSISDN" 
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."From_BankDomain"
ELSE TLTX."To_BankDomain"
END,
CASE 
WHEN TLTX."FromUsername" IS NOT NULL THEN TLTX."To_BankDomain"
ELSE TLTX."From_BankDomain"
END,
TLTX."TransactionType"

No se encontraron registros en la consulta.
[]
