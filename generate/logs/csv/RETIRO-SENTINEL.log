2025-05-14 14:29:44,797 - root - INFO - No se encontraron datos para exportar.
2025-05-14 14:29:44,799 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-RETIROS-SENTINEL-********.csv
2025-05-14 14:29:44,811 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-05-14 14:29:44,954 - root - INFO - Archivo: PDP-RETIROS-SENTINEL-********.csv subido a: s3://prd-datalake-reports-************/PDP_INTERNO/2025-05-03/RETIRO-SENTINEL/PDP-RETIROS-SENTINEL-********.csv
SELECT 
"TransferDate" as FECHA,
SUM(
CASE WHEN "To_Msisdn" = '***********' THEN "Amount"/100 
ELSE -"Amount"/100 END
) AS MONTO
FROM USR_DATALAKE.PRE_LOG_TRX
WHERE ("To_Msisdn" = '***********' OR "From_Msisdn" = '***********')
AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-05-02 00:00:00','YYYY-MM-DD HH24:MI:SS'))
AND "TransactionType" <> 'TRANSFER_TO_ANY_BANK_ACCOUNT'
GROUP BY
"TransferDate"
ORDER BY 1

Ejecutando query: SELECT 
"TransferDate" as FECHA,
SUM(
CASE WHEN "To_Msisdn" = '***********' THEN "Amount"/100 
ELSE -"Amount"/100 END
) AS MONTO
FROM USR_DATALAKE.PRE_LOG_TRX
WHERE ("To_Msisdn" = '***********' OR "From_Msisdn" = '***********')
AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-05-02 00:00:00','YYYY-MM-DD HH24:MI:SS'))
AND "TransactionType" <> 'TRANSFER_TO_ANY_BANK_ACCOUNT'
GROUP BY
"TransferDate"
ORDER BY 1

No se encontraron registros en la consulta.
[]
