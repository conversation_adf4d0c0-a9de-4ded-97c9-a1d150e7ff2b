2025-05-14 14:24:03,983 - root - INFO - <PERSON><PERSON> encontrados. Procediendo a exportar el archivo CSV.
2025-05-14 14:24:03,987 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-REPORTE-CRANDESPAGOLINEA-20250503000000.csv
2025-05-14 14:24:03,999 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-05-14 14:24:04,154 - root - INFO - Archivo: PDP-REPORTE-CRANDESPAGOLINEA-20250503000000.csv subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-05-03/CRANDES-PAGOS/PDP-REPORTE-CRANDESPAGOLINEA-20250503000000.csv
WITH
DATA_CRANDES AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@CRANDESPAGOLINEA' AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	COALESCE("Amount",0) as monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'PAYMENT'
	AND "To_Msisdn" = '51913069818'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-05-02 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_CRANDES
UNION ALL
SELECT
'TOTAL CRANDESPAGOLINEA, ' || 
CASE WHEN LENGTH(COALESCE(SUM(MONTO_REAL),0)) < 3 THEN '0.0' ELSE LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2) END
FROM DATA_CRANDES

Ejecutando query: WITH
DATA_CRANDES AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@CRANDESPAGOLINEA' AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	COALESCE("Amount",0) as monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'PAYMENT'
	AND "To_Msisdn" = '51913069818'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-05-02 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_CRANDES
UNION ALL
SELECT
'TOTAL CRANDESPAGOLINEA, ' || 
CASE WHEN LENGTH(COALESCE(SUM(MONTO_REAL),0)) < 3 THEN '0.0' ELSE LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2) END
FROM DATA_CRANDES

[<src.core.models.report_row.ReportRow object at 0x7f215111ad90>]
Archivo guardado correctamente en /home/<USER>/output/csv/PDP-REPORTE-CRANDESPAGOLINEA-20250503000000.csv
