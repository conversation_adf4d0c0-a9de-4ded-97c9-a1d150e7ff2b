SELECT 
            CAST(H.TRANSFER_DATE AS date) AS FECHA,
            SUM(
                CASE 
                    WHEN up2.msisdn = '***********' THEN H.TRANSFER_VALUE / 100
                    WHEN up1.msisdn = '***********' THEN - H.TRANSFER_VALUE / 100
                    ELSE 0
                END
            ) AS MONTO
        FROM PDP_PROD10_MAINDB.MTX_TRANSACTION_HEADER H
        INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP1
        ON H.PAYER_USER_ID = UP1.USER_ID
        INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP2
        ON H.PAYEE_USER_ID = UP2.USER_ID
        WHERE (up1.msisdn = '***********' OR up2.msisdn = '***********')
            AND TRUNC(H.TRANSFER_DATE) = TRUNC(TO_DATE('2025-05-02 00:00:00','YYYY-MM-DD HH24:MI:SS'))
            AND H.SERVICE_TYPE != 'OPTW' --'TRANSFER_TO_ANY_BANK_ACCOUNT'
            AND H.TRANSFER_STATUS = 'TS' --'COMMITTED'
        GROUP BY 
            CAST(H.TRANSFER_DATE AS date)
        ORDER BY 1

Ejecutando query: SELECT 
            CAST(H.TRANSFER_DATE AS date) AS FECHA,
            SUM(
                CASE 
                    WHEN up2.msisdn = '***********' THEN H.TRANSFER_VALUE / 100
                    WHEN up1.msisdn = '***********' THEN - H.TRANSFER_VALUE / 100
                    ELSE 0
                END
            ) AS MONTO
        FROM PDP_PROD10_MAINDB.MTX_TRANSACTION_HEADER H
        INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP1
        ON H.PAYER_USER_ID = UP1.USER_ID
        INNER JOIN PDP_PROD10_MAINDB.USER_PROFILE UP2
        ON H.PAYEE_USER_ID = UP2.USER_ID
        WHERE (up1.msisdn = '***********' OR up2.msisdn = '***********')
            AND TRUNC(H.TRANSFER_DATE) = TRUNC(TO_DATE('2025-05-02 00:00:00','YYYY-MM-DD HH24:MI:SS'))
            AND H.SERVICE_TYPE != 'OPTW' --'TRANSFER_TO_ANY_BANK_ACCOUNT'
            AND H.TRANSFER_STATUS = 'TS' --'COMMITTED'
        GROUP BY 
            CAST(H.TRANSFER_DATE AS date)
        ORDER BY 1

[<src.core.models.report_row.ReportRow object at 0x7f467effedf0>, <src.core.models.report_row.ReportRow object at 0x7f467effecd0>, <src.core.models.report_row.ReportRow object at 0x7f467effee80>, <src.core.models.report_row.ReportRow object at 0x7f467effec70>, <src.core.models.report_row.ReportRow object at 0x7f467effe970>, <src.core.models.report_row.ReportRow object at 0x7f467effef40>, <src.core.models.report_row.ReportRow object at 0x7f467effefa0>, <src.core.models.report_row.ReportRow object at 0x7f467effed60>, <src.core.models.report_row.ReportRow object at 0x7f467effed00>, <src.core.models.report_row.ReportRow object at 0x7f467effed30>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a250>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a2b0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a310>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a370>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a3d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a430>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a490>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a4f0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a550>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a5b0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a610>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a670>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a6d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a730>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a790>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a7f0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a850>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a8b0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a910>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a970>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a9d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9aa30>, <src.core.models.report_row.ReportRow object at 0x7f467ef9aa90>, <src.core.models.report_row.ReportRow object at 0x7f467ef9aaf0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ab50>, <src.core.models.report_row.ReportRow object at 0x7f467ef9abb0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ac10>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ac70>, <src.core.models.report_row.ReportRow object at 0x7f467ef9acd0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ad30>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ad90>, <src.core.models.report_row.ReportRow object at 0x7f467ef9adf0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ae50>, <src.core.models.report_row.ReportRow object at 0x7f467ef9aeb0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9af10>, <src.core.models.report_row.ReportRow object at 0x7f467ef9af70>, <src.core.models.report_row.ReportRow object at 0x7f467ef9afd0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a0a0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a070>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a160>, <src.core.models.report_row.ReportRow object at 0x7f467effee50>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a040>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f250>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f2b0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f310>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f370>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f3d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f430>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f490>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f4f0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f550>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f5b0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f610>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f670>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f6d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f730>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f790>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f7f0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f850>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f8b0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f910>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f970>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f9d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fa30>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fa90>, <src.core.models.report_row.ReportRow object at 0x7f467ef9faf0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fb50>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fbb0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fc10>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fc70>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fcd0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fd30>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fd90>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fdf0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9fe50>, <src.core.models.report_row.ReportRow object at 0x7f467ef9feb0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ff10>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ff70>, <src.core.models.report_row.ReportRow object at 0x7f467ef9ffd0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f0a0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f070>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f160>, <src.core.models.report_row.ReportRow object at 0x7f467ef9a0d0>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f040>, <src.core.models.report_row.ReportRow object at 0x7f467efa3250>, <src.core.models.report_row.ReportRow object at 0x7f467efa32b0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3310>, <src.core.models.report_row.ReportRow object at 0x7f467efa3370>, <src.core.models.report_row.ReportRow object at 0x7f467efa33d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3430>, <src.core.models.report_row.ReportRow object at 0x7f467efa3490>, <src.core.models.report_row.ReportRow object at 0x7f467efa34f0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3550>, <src.core.models.report_row.ReportRow object at 0x7f467efa35b0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3610>, <src.core.models.report_row.ReportRow object at 0x7f467efa3670>, <src.core.models.report_row.ReportRow object at 0x7f467efa36d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3730>, <src.core.models.report_row.ReportRow object at 0x7f467efa3790>, <src.core.models.report_row.ReportRow object at 0x7f467efa37f0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3850>, <src.core.models.report_row.ReportRow object at 0x7f467efa38b0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3910>, <src.core.models.report_row.ReportRow object at 0x7f467efa3970>, <src.core.models.report_row.ReportRow object at 0x7f467efa39d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3a30>, <src.core.models.report_row.ReportRow object at 0x7f467efa3a90>, <src.core.models.report_row.ReportRow object at 0x7f467efa3af0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3b50>, <src.core.models.report_row.ReportRow object at 0x7f467efa3bb0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3c10>, <src.core.models.report_row.ReportRow object at 0x7f467efa3c70>, <src.core.models.report_row.ReportRow object at 0x7f467efa3cd0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3d30>, <src.core.models.report_row.ReportRow object at 0x7f467efa3d90>, <src.core.models.report_row.ReportRow object at 0x7f467efa3df0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3e50>, <src.core.models.report_row.ReportRow object at 0x7f467efa3eb0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3f10>, <src.core.models.report_row.ReportRow object at 0x7f467efa3f70>, <src.core.models.report_row.ReportRow object at 0x7f467efa3fd0>, <src.core.models.report_row.ReportRow object at 0x7f467efa30a0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3070>, <src.core.models.report_row.ReportRow object at 0x7f467efa3160>, <src.core.models.report_row.ReportRow object at 0x7f467ef9f0d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa3040>, <src.core.models.report_row.ReportRow object at 0x7f467efa7250>, <src.core.models.report_row.ReportRow object at 0x7f467efa72b0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7310>, <src.core.models.report_row.ReportRow object at 0x7f467efa7370>, <src.core.models.report_row.ReportRow object at 0x7f467efa73d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7430>, <src.core.models.report_row.ReportRow object at 0x7f467efa7490>, <src.core.models.report_row.ReportRow object at 0x7f467efa74f0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7550>, <src.core.models.report_row.ReportRow object at 0x7f467efa75b0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7610>, <src.core.models.report_row.ReportRow object at 0x7f467efa7670>, <src.core.models.report_row.ReportRow object at 0x7f467efa76d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7730>, <src.core.models.report_row.ReportRow object at 0x7f467efa7790>, <src.core.models.report_row.ReportRow object at 0x7f467efa77f0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7850>, <src.core.models.report_row.ReportRow object at 0x7f467efa78b0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7910>, <src.core.models.report_row.ReportRow object at 0x7f467efa7970>, <src.core.models.report_row.ReportRow object at 0x7f467efa79d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7a30>, <src.core.models.report_row.ReportRow object at 0x7f467efa7a90>, <src.core.models.report_row.ReportRow object at 0x7f467efa7af0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7b50>, <src.core.models.report_row.ReportRow object at 0x7f467efa7bb0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7c10>, <src.core.models.report_row.ReportRow object at 0x7f467efa7c70>, <src.core.models.report_row.ReportRow object at 0x7f467efa7cd0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7d30>, <src.core.models.report_row.ReportRow object at 0x7f467efa7d90>, <src.core.models.report_row.ReportRow object at 0x7f467efa7df0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7e50>, <src.core.models.report_row.ReportRow object at 0x7f467efa7eb0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7f10>, <src.core.models.report_row.ReportRow object at 0x7f467efa7f70>, <src.core.models.report_row.ReportRow object at 0x7f467efa7fd0>, <src.core.models.report_row.ReportRow object at 0x7f467efa70a0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7070>, <src.core.models.report_row.ReportRow object at 0x7f467efa7160>, <src.core.models.report_row.ReportRow object at 0x7f467efa30d0>, <src.core.models.report_row.ReportRow object at 0x7f467efa7040>, <src.core.models.report_row.ReportRow object at 0x7f467efac250>, <src.core.models.report_row.ReportRow object at 0x7f467efac2b0>, <src.core.models.report_row.ReportRow object at 0x7f467efac310>, <src.core.models.report_row.ReportRow object at 0x7f467efac370>, <src.core.models.report_row.ReportRow object at 0x7f467efac3d0>, <src.core.models.report_row.ReportRow object at 0x7f467efac430>, <src.core.models.report_row.ReportRow object at 0x7f467efac490>, <src.core.models.report_row.ReportRow object at 0x7f467efac4f0>, <src.core.models.report_row.ReportRow object at 0x7f467efac550>, <src.core.models.report_row.ReportRow object at 0x7f467efac5b0>, <src.core.models.report_row.ReportRow object at 0x7f467efac610>, <src.core.models.report_row.ReportRow object at 0x7f467efac670>, <src.core.models.report_row.ReportRow object at 0x7f467efac6d0>, <src.core.models.report_row.ReportRow object at 0x7f467efac730>, <src.core.models.report_row.ReportRow object at 0x7f467efac790>, <src.core.models.report_row.ReportRow object at 0x7f467efac7f0>, <src.core.models.report_row.ReportRow object at 0x7f467efac850>, <src.core.models.report_row.ReportRow object at 0x7f467efac8b0>, <src.core.models.report_row.ReportRow object at 0x7f467efac910>, <src.core.models.report_row.ReportRow object at 0x7f467efac970>, <src.core.models.report_row.ReportRow object at 0x7f467efac9d0>, <src.core.models.report_row.ReportRow object at 0x7f467efaca30>, <src.core.models.report_row.ReportRow object at 0x7f467efaca90>, <src.core.models.report_row.ReportRow object at 0x7f467efacaf0>, <src.core.models.report_row.ReportRow object at 0x7f467efacb50>, <src.core.models.report_row.ReportRow object at 0x7f467efacbb0>, <src.core.models.report_row.ReportRow object at 0x7f467efacc10>, <src.core.models.report_row.ReportRow object at 0x7f467efacc70>, <src.core.models.report_row.ReportRow object at 0x7f467efaccd0>, <src.core.models.report_row.ReportRow object at 0x7f467efacd30>, <src.core.models.report_row.ReportRow object at 0x7f467efacd90>, <src.core.models.report_row.ReportRow object at 0x7f467efacdf0>, <src.core.models.report_row.ReportRow object at 0x7f467eface50>, <src.core.models.report_row.ReportRow object at 0x7f467efaceb0>, <src.core.models.report_row.ReportRow object at 0x7f467efacf10>, <src.core.models.report_row.ReportRow object at 0x7f467efacf70>, <src.core.models.report_row.ReportRow object at 0x7f467efacfd0>, <src.core.models.report_row.ReportRow object at 0x7f467efac0a0>, <src.core.models.report_row.ReportRow object at 0x7f467efac070>, <src.core.models.report_row.ReportRow object at 0x7f467efac160>, <src.core.models.report_row.ReportRow object at 0x7f467efa70d0>, <src.core.models.report_row.ReportRow object at 0x7f467efac040>, <src.core.models.report_row.ReportRow object at 0x7f467efb0250>, <src.core.models.report_row.ReportRow object at 0x7f467efb02b0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0310>, <src.core.models.report_row.ReportRow object at 0x7f467efb0370>, <src.core.models.report_row.ReportRow object at 0x7f467efb03d0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0430>, <src.core.models.report_row.ReportRow object at 0x7f467efb0490>, <src.core.models.report_row.ReportRow object at 0x7f467efb04f0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0550>, <src.core.models.report_row.ReportRow object at 0x7f467efb05b0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0610>, <src.core.models.report_row.ReportRow object at 0x7f467efb0670>, <src.core.models.report_row.ReportRow object at 0x7f467efb06d0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0730>, <src.core.models.report_row.ReportRow object at 0x7f467efb0790>, <src.core.models.report_row.ReportRow object at 0x7f467efb07f0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0850>, <src.core.models.report_row.ReportRow object at 0x7f467efb08b0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0910>, <src.core.models.report_row.ReportRow object at 0x7f467efb0970>, <src.core.models.report_row.ReportRow object at 0x7f467efb09d0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0a30>, <src.core.models.report_row.ReportRow object at 0x7f467efb0a90>, <src.core.models.report_row.ReportRow object at 0x7f467efb0af0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0b50>, <src.core.models.report_row.ReportRow object at 0x7f467efb0bb0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0c10>, <src.core.models.report_row.ReportRow object at 0x7f467efb0c70>, <src.core.models.report_row.ReportRow object at 0x7f467efb0cd0>, <src.core.models.report_row.ReportRow object at 0x7f467efb0d30>, <src.core.models.report_row.ReportRow object at 0x7f467efb0d90>]2025-05-14 14:29:47,476 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-05-14 14:29:47,478 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-RETIROS-WU_HUB-20250503.csv
2025-05-14 14:29:47,490 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-05-14 14:29:47,643 - root - INFO - Archivo: PDP-RETIROS-WU_HUB-20250503.csv subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-05-03/RETIRO-WU-HUB/PDP-RETIROS-WU_HUB-20250503.csv

