create PROCEDURE              SP_PRE_BAL (PARAM_FECHA IN VARCHAR)
IS 
BEGIN
---------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY - PRE BALANCES  ------------------------------------------------
	
	DELETE FROM USR_DATALAKE.USER_BALANCES WHERE FECHA_ORIGEN = TO_DATE(PARAM_FECHA,'YYYY-MM-DD');
	
	EXECUTE IMMEDIATE 'DELETE FROM USR_DATALAKE.USER_BALANCES WHERE FECHA_ORIGEN = (SELECT MAX(FECHA_ORIGEN) FROM USR_DATALAKE.USER_BALANCES) -31';
		
	INSERT INTO USR_DATALAKE.USER_BALANCES
	WITH
	PRE_WALLETS_POST AS (
	SELECT 
		MW.USER_ID,
		MW.WALLET_NUMBER,
		MW.ISSUER_ID,
		MW.USER_GRADE,
		MW.PROVIDER_ID,
		MW.PAYMENT_TYPE_ID,
		MW.USER_TYPE,
		MW.STATUS,
		MW.MSISDN,
		ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY MW.MODIFIED_ON DESC) AS ORDEN
	FROM PDP_PROD10_MAINDBBUS.MTX_WALLET MW
	INNER JOIN PDP_PROD10_MAINDB.SYS_PAYMENT_METHOD_SUBTYPES SPM ON MW.PAYMENT_TYPE_ID = SPM.PAYMENT_TYPE_ID
	WHERE mw.USER_TYPE IN ('SUBSCRIBER','CHANNEL')
	AND SPM.SUBTYPE_NAME = 'Mobile Money'
	AND TRUNC(MW.CREATED_ON) <= TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'))
	),
	BALANCE_POSTERIOR AS (
	SELECT 
		MTI.WALLET_NUMBER AS WALLET_NUMBER,  
		(MTI.PREVIOUS_BALANCE-MTI.PREVIOUS_FROZEN_BALANCE) AS BALANCE, 
		MTI.TRANSFER_ON,
		MTI.SERVICE_TYPE,
		MTI.TRANSFER_VALUE,
		MTI.TRANSFER_DATE,
		ROW_NUMBER() OVER (PARTITION BY MTI.PARTY_ID ORDER BY MTI.TRANSFER_ON ASC) AS ORDEN
	FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_ITEMS MTI
	WHERE TXN_SEQUENCE_NUMBER IN (1,2)
	AND TRUNC(TRANSFER_ON) > TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'))
	AND MTI.TRANSFER_STATUS = 'TS'
	),
	BALANCE_POST AS (
	SELECT * FROM BALANCE_POSTERIOR BP WHERE BP.ORDEN = 1
	),
	WALLETS_POST AS (
	SELECT
		mw.wallet_number WALLET_NUMBER,
		UP.ATTR7 USER_ID,
		(mwb.BALANCE-mwb.fic-mwb.frozen_amount) AS BALANCE,
		mw.ISSUER_ID,
		UP.CREATED_ON
	FROM PDP_PROD10_MAINDB.USER_PROFILE UP 
	INNER JOIN PRE_WALLETS_POST mw ON  UP.USER_ID = MW.USER_ID AND ORDEN = 1
	INNER JOIN PDP_PROD10_MAINDBBUS.MTX_WALLET_BALANCES mwb ON MW.WALLET_NUMBER  = MWB.WALLET_NUMBER
	)
	SELECT
		TO_DATE(PARAM_FECHA,'YYYY-MM-DD') AS FECHA_ORIGEN,
		W.USER_ID,
		W.ISSUER_ID,
		W.WALLET_NUMBER,
		CASE WHEN BP.WALLET_NUMBER IS NOT NULL THEN BP.BALANCE
		ELSE W.BALANCE END AS MONTO
	FROM WALLETS_POST W
	LEFT JOIN BALANCE_POST BP ON W.WALLET_NUMBER = BP.WALLET_NUMBER;
	
	COMMIT;
END SP_PRE_BAL;
/

