create PROCEDURE              SP_PRE_LOG_TRX_UPDATE
IS
BEGIN

    -- DROP de la tabla si existe
    BEGIN
        EXECUTE IMMEDIATE 'DROP TABLE USR_DATALAKE.revsersal_niubiz_21';
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -942 THEN -- Ignorar error de "tabla no existe"
                RAISE;
            END IF;
    END;

    -- Crear tabla
    EXECUTE IMMEDIATE '
        CREATE TABLE USR_DATALAKE.revsersal_niubiz_21 AS 
        SELECT "TransferID", "TransferID_Mob", "TransferDate", "ReversalID", "Comment", "Context"
        FROM USR_DATALAKE.PRE_LOG_TRX
        WHERE "TransactionType" = ''REVERSAL''
        AND "Comment" IS NULL 
        AND ("From_Msisdn" = ''51991022660'' OR "To_Msisdn" = ''51991022660'')
    ';

    -------------------------------------------------------------------

    BEGIN
        EXECUTE IMMEDIATE 'DROP TABLE USR_DATALAKE.revsersal_niubiz_21_coment';
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -942 THEN
                RAISE;
            END IF;
    END;

    EXECUTE IMMEDIATE '
        CREATE TABLE USR_DATALAKE.revsersal_niubiz_21_coment AS 
        SELECT a."TransferID", a."TransferID_Mob", a."TransferDate", a."ReversalID", 
               mth.FIELD7 AS "Comment", a."Context"
        FROM revsersal_niubiz_21 a
        INNER JOIN PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER mth 
        ON a."ReversalID" = mth.TRANSFER_ID
    ';

    -------------------------------------------------------------------

    UPDATE USR_DATALAKE.PRE_LOG_TRX
    SET "Context" = 'http-fcompartamos_niubiz_interope'
    WHERE "Context" = 'internal'
    AND "TransactionType" = 'REVERSAL'
    AND ("From_Msisdn" = '51991022660' OR "To_Msisdn" = '51991022660');

    COMMIT;

    -------------------------------------------------------------------

    MERGE INTO USR_DATALAKE.PRE_LOG_TRX a
    USING (
        SELECT "TransferID", "Comment"
        FROM revsersal_niubiz_21_coment
    ) b
    ON (a."TransferID" = b."TransferID")
    WHEN MATCHED THEN
    UPDATE SET a."Comment" = b."Comment";

    COMMIT;

END SP_PRE_LOG_TRX_UPDATE;

/

