create PROCEDURE              SP_USER_AUTH_DAY (PARAM_FECHA IN VARCHAR)
IS 
BEGIN
---------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY - LOG - USUARIOS  ------------------------------------------------
	
	EXECUTE IMMEDIATE 'TRUNCATE TABLE USR_DATALAKE.USER_AUTH_CHANGE_HISTORY';
	INSERT INTO USR_DATALAKE.USER_AUTH_CHANGE_HISTORY
	SELECT 
		uach.MODIFIED_ON,
		uach.MODIFICATION_TYPE,
		uach.MODIFIED_BY,
		AUTHENTICATION_ID
	FROM PDP_PROD10_MAINDB.USER_AUTH_CHANGE_HISTORY uach
	WHERE trunc(uach.MODIFIED_ON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'))
	AND uach.AUTHENTICATION_TYPE = 'PIN';

	COMMIT;

END SP_USER_AUTH_DAY;
/

