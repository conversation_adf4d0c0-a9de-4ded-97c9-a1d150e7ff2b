create PROCEDURE              SP_FIELD7 (
	PARA<PERSON>_FECHA IN VARCHAR,
	OUT_RESULTADO OUT VARCHAR
)
IS
	v_count INTEGER;
BEGIN
----------------------------------------------------------------------------------------------------------------	
---------------- ACTUALIZACION PARA LO QUE NO SON PROCESADOS POR EL APK
	
	OUT_RESULTADO := 'Procesamiento iniciado';

    -- Verificar si existen filas que cumplen con la condición
    SELECT COUNT(*) 
    INTO v_count
    FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
    WHERE TRUNC(TRANSFER_DATE) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'))
    AND FIELD7 IS NULL;
   
   IF v_count = 0 THEN
        OUT_RESULTADO := 'No se encontraron registros para procesar';
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;
	
	FOR r IN (
        SELECT ROWID
        FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
        WHERE TRUNC(TRANSFER_DATE) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'))
        AND FIELD7 IS NULL
        FOR UPDATE -- Bloqueamos las filas para evitar problemas de concurrencia
    ) LOOP  
		UPDATE PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER  
	    SET FIELD7 = TO_CHAR(FLOOR((SYSDATE - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400000 +
	                            MOD(DBMS_UTILITY.GET_TIME, 1000)))  
	                         || LPAD(TO_CHAR(FLOOR(DBMS_RANDOM.VALUE(0, 100))), 2, '0')
	        WHERE ROWID = r.ROWID;
	 
	        DBMS_LOCK.SLEEP(0.005);  -- Espera de 5ms  
	    END LOOP;  
	 
	    COMMIT;  -- Confirmamos TODAS las actualizaciones al final  
	
		OUT_RESULTADO := 'Registros procesados correctamente';

	EXCEPTION
    -- Manejo de excepciones en caso de errores
    WHEN OTHERS THEN
        OUT_RESULTADO := 'Error en el procesamiento: ' || SQLERRM;
        ROLLBACK;
 
END SP_FIELD7;
/

