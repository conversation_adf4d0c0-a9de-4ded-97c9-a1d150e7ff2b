create PROCEDURE              SP_ID_LOG_USR (
    PARAM_FECHA IN VARCHAR,
    OUT_RESULTADO OUT VARCHAR
)
IS
    v_count INTEGER;  -- Variable para contar los registros
BEGIN
    OUT_RESULTADO := 'Procesamiento iniciado';

    ----------------------------------------------------------------------------------------------------------------    
    ---------------- ACTUALIZACION PARA LO QUE NO SON PROCESADOS POR EL APK
    -- Verificamos si existen registros que procesar
    SELECT COUNT(*) 
    INTO v_count
    FROM USR_DATALAKE.LOG_USR
    WHERE TRUNC(CREATEDON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS')) 
    AND USERHISTID IS NULL;

    IF v_count = 0 THEN
        OUT_RESULTADO := 'No se encontraron registros para procesar';
        RETURN;
    END IF;

    DECLARE  
        CURSOR c_trans IS  
            SELECT ROWID 
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS')) 
            AND USERHISTID IS NULL;
        
        counter INT := 0;
    BEGIN  
        FOR r IN c_trans LOOP
            UPDATE USR_DATALAKE.LOG_USR  
            SET USERHISTID = LPAD(TO_CHAR(FLOOR((SYSDATE - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400000 + 
                             MOD(DBMS_UTILITY.GET_TIME, 1000))), 13, '0')  
                             || LPAD(TO_CHAR(FLOOR(DBMS_RANDOM.VALUE(0, 100))), 2, '0')  
            WHERE ROWID = r.ROWID;
            
            counter := counter + 1;
            
            IF counter >= 15000 THEN
                -- Realizamos un COMMIT cada 15000 registros
                COMMIT;
                counter := 0;
            END IF;
            
            DBMS_LOCK.SLEEP(0.005);  -- Espera de 5ms  
        END LOOP;
       
        OUT_RESULTADO := 'Registros procesados correctamente';
    END;
END SP_ID_LOG_USR;
/

