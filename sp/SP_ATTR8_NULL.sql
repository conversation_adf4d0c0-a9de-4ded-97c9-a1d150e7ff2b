create PROCEDURE              SP_ATTR8_NULL (
    PARAM_FECHA IN VARCHAR,
    OUT_RESULTADO OUT VARCHAR -- Parámetro de salida
)
IS
    v_count INTEGER;
BEGIN
    -- Inicializar el mensaje de salida
    OUT_RESULTADO := 'Procesamiento iniciado';

    -- Verificar si existen filas que cumplen con la condición
    SELECT COUNT(*) 
    INTO v_count
    FROM PDP_PROD10_MAINDB.USER_PROFILE
    WHERE TRUNC(CREATED_ON) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'))  
    AND ATTR8 IS NULL;
    
    -- Si no hay filas que cumplan con la condición, salir del procedimiento
    IF v_count = 0 THEN
        OUT_RESULTADO := 'No se encontraron registros para procesar';
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;

    -- Proceso de actualización de los registros
    FOR r IN (
        SELECT ROWID
        FROM PDP_PROD10_MAINDB.USER_PROFILE
        WHERE TRUNC(CREATED_ON) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'))  
        AND ATTR8 IS NULL
        FOR UPDATE -- Bloqueamos las filas para evitar problemas de concurrencia
    ) LOOP
        -- Actualización de ATTR8
        UPDATE PDP_PROD10_MAINDB.USER_PROFILE  
        SET ATTR8 = TO_CHAR(FLOOR((SYSDATE - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400000 + 
                                 MOD(DBMS_UTILITY.GET_TIME, 1000)))  
                                 || LPAD(TO_CHAR(FLOOR(DBMS_RANDOM.VALUE(0, 100))), 2, '0')  
        WHERE ROWID = r.ROWID;

        -- Espera de 5ms para evitar problemas de concurrencia
        DBMS_LOCK.SLEEP(0.005);  
    END LOOP;

    -- Confirmar todas las actualizaciones
    COMMIT;

    -- Mensaje de salida cuando los registros se procesan correctamente
    OUT_RESULTADO := 'Registros procesados correctamente';

EXCEPTION
    -- Manejo de excepciones en caso de errores
    WHEN OTHERS THEN
        OUT_RESULTADO := 'Error en el procesamiento: ' || SQLERRM;
        ROLLBACK;  -- En caso de error, hacemos un rollback de la transacción
END SP_ATTR8_NULL;
/

