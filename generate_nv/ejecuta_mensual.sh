#!/bin/bash

# Variables de entorno
export PRIVATE_KEY_PATH="/home/<USER>/generate/FileSigner/pdp_sign.key"
export PRIVATE_CRT_PATH="/home/<USER>/generate/FileSigner/SignFileNC.crt"
export OUTPUT_ROUTE="/home/<USER>/output/excel/"
export OUTPUT_ROUTE_CSV="/home/<USER>/output/csv/"

# Definir arrays
values32=("32B-I" "32B-II" "32B-III" "32B-IV" "32B-V")
values=("COMISIONES-BIMER" "P2P" "CASHIN" "CASHOUT" "AGENTES-BIM" "SERVICIOS-DIRECTOS" "COMERCIOS" "RECARGAS")
valuescsv=("INTEROPE-COBRAR-PDF" "INTEROPE-PAGAR-PDF" "INTEROPE-COBRAR" "INTEROPE-PAGAR" "32B-I" "32B-II" "32B-III" "32B-IV" "32B-V" "COMISIONES")

ROUTE_CSV="/home/<USER>/output/csv"
TARGET_PATH="/home/<USER>/output/load_rds"

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")

cd /home/<USER>/generate/

# Crear carpetas de logs
mkdir -p logs/{excel,csv,log_transacciones,reports32a_b,account_balances,log_usuarios,prepare,reporte_conciliacion,csv_to_pdf,prepare_rds,mysql_reports}

echo "== Instalando dependencias si faltan =="
pip install -r requirements.txt

#echo "== Preparando datos preliminares =="
#python3 prepare/main.py "$fecha" > "logs/prepare/PRE-REPORTES.log" 2>&1 &
#wait

echo "== Generando REPORTES EXCEL =="
for value in "${values[@]}"; do
    echo "Iniciando: $value"
    python3 exports_excel/main.py "$value" "$fecha" > "logs/excel/${value}.log" 2>&1 &
done

echo "== Generando REPORTES CSV =="
for value in "${valuescsv[@]}"; do
    echo "Iniciando: $value"
    python3 exports_csv/main.py "$value" "$fecha" > "logs/csv/${value}.log" 2>&1 &
done
wait

echo "== Generando REPORTES 32x =="
cd /home/<USER>/generate/reports32a-b/
for value in "${values32[@]}"; do
    echo "Iniciando: $value"
    python3 main.py "$value" "$fecha" > "/home/<USER>/generate/logs/reports32a_b/${value}.log" 2>&1 &
done
wait

echo "== Ejecutando CREACION DE PDFS =="
cd /home/<USER>/generate/csv_to_pdf/
mv -f /home/<USER>/output/csv/*PDF* /home/<USER>/output/pdf
python3 main.py "$fecha" > "/home/<USER>/generate/logs/csv_to_pdf/CSV_TO_PDF.log" 2>&1 &
wait

echo ""
echo "== RESUMEN DE ESTADO =="
for value in "${values[@]}"; do
    if grep -qi "error" "logs/excel/${value}.log"; then
        echo "❌ $value (ver logs/excel/${value}.log)"
    else
        echo "✅ $value"
    fi
done

for value in "${valuescsv[@]}"; do
    if grep -qi "error" "logs/csv/${value}.log"; then
        echo "❌ $value (ver logs/csv/${value}.log)"
    else
        echo "✅ $value"
    fi
done

for value in "${values32[@]}"; do
    if grep -qi "error" "logs/reports32a_b/${value}.log"; then
        echo "❌ $value (ver logs/reports32a_b/${value}.log)"
    else
        echo "✅ $value"
    fi
done

echo "== FIN DE EJECUCIÓN =="

