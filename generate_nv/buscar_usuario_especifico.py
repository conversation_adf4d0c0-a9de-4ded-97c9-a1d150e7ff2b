#!/usr/bin/env python3
"""
Script para buscar específicamente el usuario faltante en los archivos parquet
"""
import pandas as pd
import os

def buscar_usuario_especifico():
    """Busca el usuario específico en los archivos parquet principales"""
    print("🔍 BÚSQUEDA ESPECÍFICA DEL USUARIO FALTANTE")
    print("=" * 50)
    
    # Datos del registro faltante
    documento = "71793435"
    celular = "51907368782"
    fecha_hora = "2025-06-09 22:52:32"
    
    print(f"🎯 BUSCANDO:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    print(f"   • Fecha/Hora: {fecha_hora}")
    
    # Archivos principales a revisar
    archivos_principales = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/LOG_USR.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/LOG_USR_DEDUPLICATED.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/USER_DATA_TRX.parquet",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/USER_MODIFICATION_DAY.parquet"
    ]
    
    encontrado_en = []
    
    for archivo in archivos_principales:
        if os.path.exists(archivo):
            print(f"\n📄 Analizando: {os.path.basename(archivo)}")
            try:
                resultado = analizar_archivo_parquet(archivo, documento, celular)
                if resultado:
                    encontrado_en.append((archivo, resultado))
                    print(f"   ✅ USUARIO ENCONTRADO!")
                    for reg in resultado:
                        print(f"      - Documento: {reg.get('documento', 'N/A')}")
                        print(f"      - Celular: {reg.get('celular', 'N/A')}")
                        print(f"      - Fecha: {reg.get('fecha', 'N/A')}")
                        print(f"      - Otros datos: {reg.get('otros', 'N/A')}")
                else:
                    print(f"   ❌ Usuario no encontrado")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        else:
            print(f"❌ Archivo no existe: {archivo}")
    
    # Buscar también en archivos CSV del pipeline
    print(f"\n📄 BÚSQUEDA EN CSV DEL PIPELINE:")
    archivo_csv = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/LOG-USUARIOS-FCOMPARTAMOS-20250609.csv"
    
    if os.path.exists(archivo_csv):
        print(f"   🔍 Analizando: {os.path.basename(archivo_csv)}")
        try:
            resultado = buscar_en_csv_pipeline(archivo_csv, documento, celular)
            if resultado:
                encontrado_en.append((archivo_csv, resultado))
                print(f"   ✅ USUARIO ENCONTRADO!")
                for reg in resultado:
                    print(f"      - Operación: {reg.get('operacion', 'N/A')}")
                    print(f"      - TransactionID: {reg.get('transactionid', 'N/A')}")
                    print(f"      - Fecha: {reg.get('fecha', 'N/A')}")
                    print(f"      - Documento: {reg.get('documento', 'N/A')}")
                    print(f"      - Celular: {reg.get('celular', 'N/A')}")
            else:
                print(f"   ❌ Usuario no encontrado")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Resumen final
    print(f"\n📊 RESUMEN FINAL:")
    if encontrado_en:
        print(f"   ✅ Usuario encontrado en {len(encontrado_en)} archivos:")
        for archivo, registros in encontrado_en:
            print(f"      📄 {os.path.basename(archivo)} ({len(registros)} registros)")
        
        print(f"\n💡 CONCLUSIÓN:")
        print(f"   El usuario EXISTE en los datos de origen, pero se perdió durante el procesamiento.")
        print(f"   Revisar la lógica del pipeline para identificar dónde se filtró.")
    else:
        print(f"   ❌ Usuario NO encontrado en ningún archivo")
        print(f"\n💡 CONCLUSIÓN:")
        print(f"   El usuario NO EXISTE en los datos de origen del pipeline.")
        print(f"   La diferencia puede deberse a:")
        print(f"   1. Datos de origen diferentes entre Oracle y S3")
        print(f"   2. Filtros de fecha/hora en el pipeline")
        print(f"   3. El registro se creó después de la extracción de datos")

def analizar_archivo_parquet(archivo_path, documento, celular):
    """Analiza un archivo parquet específico"""
    try:
        df = pd.read_parquet(archivo_path)
        
        print(f"      Registros totales: {len(df):,}")
        print(f"      Columnas: {list(df.columns)}")
        
        registros_encontrados = []
        
        # Buscar por documento
        if 'DOCUMENTO' in df.columns:
            mask_doc = df['DOCUMENTO'].astype(str) == documento
            registros_doc = df[mask_doc]
            
            if len(registros_doc) > 0:
                print(f"      Registros con documento {documento}: {len(registros_doc)}")
                
                for _, registro in registros_doc.iterrows():
                    reg_info = {
                        'documento': registro.get('DOCUMENTO', 'N/A'),
                        'celular': registro.get('MSISDN', registro.get('WALLET_NUMBER', 'N/A')),
                        'fecha': registro.get('CREATEDON', registro.get('CREATED_ON', registro.get('CREATED_AT', 'N/A'))),
                        'otros': {col: registro[col] for col in df.columns if col not in ['DOCUMENTO', 'MSISDN', 'CREATEDON']}
                    }
                    registros_encontrados.append(reg_info)
        
        # Buscar por celular si no se encontró por documento
        if not registros_encontrados:
            columnas_celular = ['MSISDN', 'WALLET_NUMBER', 'CELULAR']
            for col_cel in columnas_celular:
                if col_cel in df.columns:
                    mask_cel = df[col_cel].astype(str) == celular
                    registros_cel = df[mask_cel]
                    
                    if len(registros_cel) > 0:
                        print(f"      Registros con celular {celular}: {len(registros_cel)}")
                        
                        for _, registro in registros_cel.iterrows():
                            reg_info = {
                                'documento': registro.get('DOCUMENTO', 'N/A'),
                                'celular': registro.get(col_cel, 'N/A'),
                                'fecha': registro.get('CREATEDON', registro.get('CREATED_ON', registro.get('CREATED_AT', 'N/A'))),
                                'otros': {col: registro[col] for col in df.columns if col not in ['DOCUMENTO', col_cel, 'CREATEDON']}
                            }
                            registros_encontrados.append(reg_info)
                        break
        
        return registros_encontrados if registros_encontrados else None
        
    except Exception as e:
        raise e

def buscar_en_csv_pipeline(archivo_path, documento, celular):
    """Busca en el archivo CSV del pipeline"""
    try:
        df = pd.read_csv(archivo_path, header=None, low_memory=False)
        
        print(f"      Registros totales: {len(df):,}")
        print(f"      Columnas: {len(df.columns)}")
        
        registros_encontrados = []
        
        if len(df.columns) >= 7:
            # Estructura: OPERACION, TRANSACTIONID, FECHA_HORA, CANAL, TIPODOCUMENTO, DOCUMENTO, CELULAR
            mask = (df.iloc[:, 5].astype(str) == documento) | (df.iloc[:, 6].astype(str) == celular)
            registros = df[mask]
            
            print(f"      Registros que coinciden: {len(registros)}")
            
            for _, registro in registros.iterrows():
                reg_info = {
                    'operacion': registro.iloc[0] if len(registro) > 0 else 'N/A',
                    'transactionid': registro.iloc[1] if len(registro) > 1 else 'N/A',
                    'fecha': registro.iloc[2] if len(registro) > 2 else 'N/A',
                    'documento': registro.iloc[5] if len(registro) > 5 else 'N/A',
                    'celular': registro.iloc[6] if len(registro) > 6 else 'N/A'
                }
                registros_encontrados.append(reg_info)
        
        return registros_encontrados if registros_encontrados else None
        
    except Exception as e:
        raise e

def verificar_filtros_fecha():
    """Verifica si hay filtros de fecha que puedan estar excluyendo el registro"""
    print(f"\n🔍 VERIFICACIÓN DE FILTROS DE FECHA:")
    
    fecha_registro = "2025-06-09 22:52:32"
    hora_registro = "22:52:32"
    
    print(f"   📅 Fecha del registro faltante: {fecha_registro}")
    print(f"   🕐 Hora del registro: {hora_registro} (22:52 PM)")
    
    # Verificar si es una hora muy tardía que podría estar siendo filtrada
    hora_num = int(hora_registro.split(':')[0])
    if hora_num >= 22:
        print(f"   ⚠️  ALERTA: Registro en hora nocturna ({hora_num}:XX)")
        print(f"   💡 Posible causa: Filtros que excluyen transacciones nocturnas")
        print(f"   🔍 Revisar si el pipeline tiene filtros de horario de negocio")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN ESPECÍFICA DEL USUARIO FALTANTE")
    print("=" * 60)
    
    buscar_usuario_especifico()
    verificar_filtros_fecha()
    
    print(f"\n✅ Investigación completada")
    print(f"\n📋 PRÓXIMOS PASOS:")
    print(f"   1. Si el usuario se encontró en origen: Revisar lógica del pipeline")
    print(f"   2. Si no se encontró: Verificar datos de origen vs Oracle")
    print(f"   3. Revisar filtros de fecha/hora en el pipeline")
    print(f"   4. Verificar si hay filtros de horario de negocio")
