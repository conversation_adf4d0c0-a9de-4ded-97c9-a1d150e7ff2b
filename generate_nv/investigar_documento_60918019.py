#!/usr/bin/env python3
"""
🥷 INVESTIGACIÓN ESPECÍFICA DOCUMENTO 60918019
Objetivo: Encontrar por qué este documento no cuadra y corregir la solución ninja
"""
import pandas as pd
import duckdb
from datetime import datetime

def investigar_documento_60918019():
    """Investigación completa del documento 60918019"""
    print("🥷 INVESTIGACIÓN DOCUMENTO 60918019")
    print("=" * 50)
    
    documento = "60918019"
    
    # Archivos
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609210505.csv"
    
    print(f"🎯 Documento a investigar: {documento}")
    print(f"📄 LOG_USR original: {log_usr_original.split('/')[-1]}")
    print(f"📄 Oracle: {archivo_oracle.split('/')[-1]}")
    print(f"📄 Modernizado: {archivo_modernizado.split('/')[-1]}")
    
    # 1. Verificar si existe en Oracle
    print(f"\n1️⃣ VERIFICACIÓN EN ORACLE:")
    resultado_oracle = verificar_en_oracle(archivo_oracle, documento)
    
    # 2. Verificar si existe en modernizado
    print(f"\n2️⃣ VERIFICACIÓN EN MODERNIZADO:")
    resultado_modernizado = verificar_en_modernizado(archivo_modernizado, documento)
    
    # 3. Analizar datos originales
    print(f"\n3️⃣ ANÁLISIS DE DATOS ORIGINALES:")
    datos_originales = analizar_datos_originales(log_usr_original, documento)
    
    # 4. Aplicar lógica ninja y verificar
    print(f"\n4️⃣ APLICAR LÓGICA NINJA:")
    resultado_ninja = aplicar_logica_ninja_individual(log_usr_original, documento)
    
    # 5. Diagnóstico completo
    print(f"\n5️⃣ DIAGNÓSTICO COMPLETO:")
    diagnosticar_problema(documento, resultado_oracle, resultado_modernizado, datos_originales, resultado_ninja)

def verificar_en_oracle(archivo_oracle, documento):
    """Verifica si el documento existe en Oracle"""
    try:
        df = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        reg = df[df['DOCUMENTO'].astype(str) == documento]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            print(f"   ✅ ENCONTRADO EN ORACLE:")
            print(f"      Operación: {registro['OPERACION']}")
            print(f"      Timestamp: {registro['FECHA_HORA']}")
            print(f"      TID: {registro['TRANSACTIONID']}")
            print(f"      Celular: {registro['CELULAR']}")
            print(f"      Empresa: {registro['EMPRESA']}")
            
            return {
                'encontrado': True,
                'timestamp': registro['FECHA_HORA'],
                'operacion': registro['OPERACION'],
                'transactionid': registro['TRANSACTIONID'],
                'celular': registro['CELULAR'],
                'empresa': registro['EMPRESA']
            }
        else:
            print(f"   ❌ NO ENCONTRADO EN ORACLE")
            return {'encontrado': False}
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {'encontrado': False, 'error': str(e)}

def verificar_en_modernizado(archivo_modernizado, documento):
    """Verifica si el documento existe en modernizado"""
    try:
        df = pd.read_csv(archivo_modernizado, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        reg = df[df['DOCUMENTO'].astype(str) == documento]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            print(f"   ✅ ENCONTRADO EN MODERNIZADO:")
            print(f"      Operación: {registro['OPERACION']}")
            print(f"      Timestamp: {registro['FECHA_HORA']}")
            print(f"      TID: {registro['TRANSACTIONID']}")
            print(f"      Celular: {registro['CELULAR']}")
            print(f"      Empresa: {registro['EMPRESA']}")
            
            return {
                'encontrado': True,
                'timestamp': registro['FECHA_HORA'],
                'operacion': registro['OPERACION'],
                'transactionid': registro['TRANSACTIONID'],
                'celular': registro['CELULAR'],
                'empresa': registro['EMPRESA']
            }
        else:
            print(f"   ❌ NO ENCONTRADO EN MODERNIZADO")
            return {'encontrado': False}
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {'encontrado': False, 'error': str(e)}

def analizar_datos_originales(log_usr_path, documento):
    """Analiza los datos originales del documento"""
    try:
        conn = duckdb.connect()
        
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
        FROM read_parquet('{log_usr_path}')
        WHERE DOCUMENTO = '{documento}'
        ORDER BY CREATEDON
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"   📊 Total registros en datos originales: {len(result)}")
        
        if len(result) > 0:
            print(f"   📋 Todos los registros:")
            registros = []
            for i, (requesttype, createdon, userhistid, msisdn, bankdomain) in enumerate(result, 1):
                dt = datetime.strptime(str(createdon), '%Y-%m-%d %H:%M:%S')
                
                # Clasificar período
                if 6 <= dt.hour < 12:
                    periodo = "MAÑANA"
                    prioridad = 1
                elif 0 <= dt.hour < 6:
                    periodo = "MADRUGADA"
                    prioridad = 2
                elif 12 <= dt.hour < 18:
                    periodo = "TARDE"
                    prioridad = 3
                else:
                    periodo = "NOCHE"
                    prioridad = 4
                
                registro = {
                    'orden': i,
                    'timestamp': str(createdon),
                    'requesttype': requesttype,
                    'userhistid': userhistid,
                    'msisdn': msisdn,
                    'bankdomain': bankdomain,
                    'periodo': periodo,
                    'prioridad': prioridad,
                    'hora': dt.hour
                }
                
                registros.append(registro)
                
                print(f"      {i}. {createdon} ({periodo} - P{prioridad}) - {requesttype} - {userhistid}")
                print(f"         MSISDN: {msisdn} - BankDomain: {bankdomain}")
            
            conn.close()
            return registros
        else:
            print(f"   ❌ No se encontraron registros en datos originales")
            conn.close()
            return []
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def aplicar_logica_ninja_individual(log_usr_path, documento):
    """Aplica la lógica ninja específicamente a este documento"""
    try:
        conn = duckdb.connect()
        
        # Aplicar lógica ninja exacta
        ninja_query = f"""
        WITH registros_con_prioridad AS (
            SELECT *,
                CASE 
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1  -- MAÑANA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2   -- MADRUGADA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3 -- TARDE
                    ELSE 4  -- NOCHE (18-23)
                END as periodo_prioridad,
                CASE 
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 'MAÑANA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 'MADRUGADA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 'TARDE'
                    ELSE 'NOCHE'
                END as periodo_nombre,
                ROW_NUMBER() OVER (
                    PARTITION BY USERHISTID, REQUESTTYPE
                    ORDER BY 
                        CASE 
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2
                            WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3
                            ELSE 4
                        END ASC,
                        CREATEDON DESC
                ) as rn_final
            FROM read_parquet('{log_usr_path}')
            WHERE DOCUMENTO = '{documento}'
        )
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN, periodo_prioridad, periodo_nombre
        FROM registros_con_prioridad
        WHERE rn_final = 1
        ORDER BY CREATEDON
        """
        
        result = conn.execute(ninja_query).fetchall()
        
        print(f"   📊 Registros después de lógica ninja: {len(result)}")
        
        if len(result) > 0:
            print(f"   📋 Registros seleccionados por ninja:")
            ninja_registros = []
            for requesttype, createdon, userhistid, msisdn, bankdomain, prioridad, periodo in result:
                registro = {
                    'timestamp': str(createdon),
                    'requesttype': requesttype,
                    'userhistid': userhistid,
                    'msisdn': msisdn,
                    'bankdomain': bankdomain,
                    'prioridad': prioridad,
                    'periodo': periodo
                }
                ninja_registros.append(registro)
                
                print(f"      • {createdon} ({periodo} - P{prioridad}) - {requesttype} - {userhistid}")
                print(f"        MSISDN: {msisdn} - BankDomain: {bankdomain}")
            
            conn.close()
            return ninja_registros
        else:
            print(f"   ❌ Lógica ninja no seleccionó ningún registro")
            conn.close()
            return []
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def diagnosticar_problema(documento, oracle, modernizado, originales, ninja):
    """Diagnostica el problema específico del documento"""
    
    print(f"   🔍 DIAGNÓSTICO DOCUMENTO {documento}:")
    
    # Verificar existencia
    oracle_existe = oracle.get('encontrado', False)
    modernizado_existe = modernizado.get('encontrado', False)
    originales_existen = len(originales) > 0
    ninja_funciona = len(ninja) > 0
    
    print(f"      📊 Existencia:")
    print(f"         Oracle: {'✅' if oracle_existe else '❌'}")
    print(f"         Modernizado: {'✅' if modernizado_existe else '❌'}")
    print(f"         Datos originales: {'✅' if originales_existen else '❌'} ({len(originales)} registros)")
    print(f"         Lógica ninja: {'✅' if ninja_funciona else '❌'} ({len(ninja)} registros)")
    
    # Diagnóstico específico
    if not oracle_existe:
        print(f"      🚨 PROBLEMA: Documento no existe en Oracle")
        print(f"         CAUSA: Documento puede estar filtrado en Oracle")
        print(f"         SOLUCIÓN: Verificar filtros Oracle vs pipeline")
    
    elif not modernizado_existe and originales_existen:
        print(f"      🚨 PROBLEMA: Documento existe en originales pero no en modernizado")
        print(f"         CAUSA: Filtros del pipeline eliminan el documento")
        print(f"         SOLUCIÓN: Revisar filtros de calidad de datos")
    
    elif not ninja_funciona and originales_existen:
        print(f"      🚨 PROBLEMA: Lógica ninja no funciona para este documento")
        print(f"         CAUSA: Problema en la lógica de deduplicación")
        print(f"         SOLUCIÓN: Revisar lógica ninja")
    
    elif oracle_existe and modernizado_existe:
        # Comparar timestamps
        oracle_ts = oracle.get('timestamp', '')
        modernizado_ts = modernizado.get('timestamp', '')
        
        if oracle_ts != modernizado_ts:
            print(f"      🚨 PROBLEMA: Timestamps diferentes")
            print(f"         Oracle: {oracle_ts}")
            print(f"         Modernizado: {modernizado_ts}")
            print(f"         CAUSA: Lógica ninja no replica Oracle correctamente")
            print(f"         SOLUCIÓN: Ajustar lógica ninja para este caso")
        else:
            print(f"      ✅ DOCUMENTO HOMOLOGA CORRECTAMENTE")
    
    else:
        print(f"      🚨 PROBLEMA: Caso no identificado")
        print(f"         SOLUCIÓN: Investigación adicional requerida")

if __name__ == "__main__":
    print("🥷 INVESTIGACIÓN ESPECÍFICA - DOCUMENTO 60918019")
    print("=" * 80)
    
    investigar_documento_60918019()
    
    print(f"\n🎯 OBJETIVO: Identificar por qué este documento no cuadra")
    print(f"📋 RESULTADO: Diagnóstico completo para corrección")
    
    print(f"\n✅ Investigación completada")
