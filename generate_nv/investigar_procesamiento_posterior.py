#!/usr/bin/env python3
"""
Investigar qué pasa en el procesamiento posterior que cambia 09:41:55 a 00:52:48
"""
import pandas as pd
import os

def investigar_procesamiento_posterior():
    """Investiga el procesamiento posterior que cambia el resultado"""
    print("🔍 INVESTIGACIÓN DEL PROCESAMIENTO POSTERIOR")
    print("=" * 55)
    
    documento = "76730654"
    
    print(f"🎯 Documento objetivo: {documento}")
    print(f"❓ Pregunta: ¿Por qué LOG_USR_DEDUPLICATED tiene 09:41:55 pero CSV final tiene 00:52:48?")
    
    # 1. Verificar LOG_USR_DEDUPLICATED.parquet
    print(f"\n1️⃣ VERIFICACIÓN LOG_USR_DEDUPLICATED.parquet:")
    verificar_log_usr_deduplicated(documento)
    
    # 2. Verificar archivo CSV final
    print(f"\n2️⃣ VERIFICACIÓN ARCHIVO CSV FINAL:")
    verificar_csv_final(documento)
    
    # 3. <PERSON><PERSON><PERSON> la transformación
    print(f"\n3️⃣ ANÁLISIS DE LA TRANSFORMACIÓN:")
    analizar_transformacion(documento)

def verificar_log_usr_deduplicated(documento):
    """Verifica el documento en LOG_USR_DEDUPLICATED.parquet"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/LOG_USR_DEDUPLICATED.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            
            print(f"   📄 LOG_USR_DEDUPLICATED.parquet:")
            print(f"      Total registros: {len(df):,}")
            
            # Buscar documento específico
            mask = df['DOCUMENTO'].astype(str) == documento
            registros = df[mask]
            
            print(f"      🎯 Registros del documento {documento}: {len(registros)}")
            
            if len(registros) > 0:
                for _, reg in registros.iterrows():
                    print(f"         {reg['REQUESTTYPE']} - {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                    print(f"         MSISDN: {reg['MSISDN']} - BankDomain: {reg['BANKDOMAIN']}")
                    
                    # Verificar si es CHANGE_AUTH_FACTOR
                    if reg['REQUESTTYPE'] == 'CHANGE_AUTH_FACTOR':
                        print(f"         ✅ CHANGE_AUTH_FACTOR encontrado con fecha: {reg['CREATEDON']}")
            else:
                print(f"         ❌ Documento no encontrado")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
    else:
        print(f"   ❌ Archivo no encontrado")

def verificar_csv_final(documento):
    """Verifica el documento en el archivo CSV final"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-**************.csv"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_csv(archivo, header=None)
            
            # Asignar columnas
            columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                       'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
            
            df.columns = columnas[:len(df.columns)]
            
            print(f"   📄 CSV Final:")
            print(f"      Total registros: {len(df):,}")
            
            # Buscar documento específico
            mask = df['DOCUMENTO'].astype(str) == documento
            registros = df[mask]
            
            print(f"      🎯 Registros del documento {documento}: {len(registros)}")
            
            if len(registros) > 0:
                for _, reg in registros.iterrows():
                    print(f"         {reg['OPERACION']} - {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                    print(f"         Celular: {reg['CELULAR']} - Empresa: {reg['EMPRESA']}")
                    
                    if reg['OPERACION'] == 'CPIN':
                        print(f"         ✅ CPIN encontrado con fecha: {reg['FECHA_HORA']}")
            else:
                print(f"         ❌ Documento no encontrado")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")
    else:
        print(f"   ❌ Archivo no encontrado")

def analizar_transformacion(documento):
    """Analiza la transformación entre archivos"""
    print(f"   🔍 Análisis de la transformación:")
    
    # Verificar si hay múltiples registros en LOG_USR original
    archivo_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/********/LOG_USR.parquet"
    
    if os.path.exists(archivo_original):
        try:
            df_orig = pd.read_parquet(archivo_original)
            
            # Buscar todos los registros del documento
            mask = df_orig['DOCUMENTO'].astype(str) == documento
            registros_orig = df_orig[mask]
            
            print(f"      📊 LOG_USR.parquet original:")
            print(f"         Total registros del documento: {len(registros_orig)}")
            
            if len(registros_orig) > 0:
                # Agrupar por USERHISTID y REQUESTTYPE
                grupos = registros_orig.groupby(['USERHISTID', 'REQUESTTYPE'])
                
                print(f"         📋 Grupos USERHISTID + REQUESTTYPE:")
                for (userhistid, requesttype), grupo in grupos:
                    print(f"            {userhistid} + {requesttype}: {len(grupo)} registros")
                    for _, reg in grupo.iterrows():
                        print(f"               {reg['CREATEDON']}")
                
                # Verificar si el procesamiento posterior está usando una lógica diferente
                print(f"\n      💡 Hipótesis:")
                print(f"         1. LOG_USR_DEDUPLICATED toma el más reciente por (USERHISTID, REQUESTTYPE)")
                print(f"         2. Procesamiento posterior puede estar agrupando diferente")
                print(f"         3. Puede estar usando otra clave de agrupación")
                print(f"         4. Puede estar aplicando otro filtro temporal")
                
                # Verificar si hay diferencias en otros campos
                print(f"\n      🔍 Verificando otros campos que pueden afectar agrupación:")
                for _, reg in registros_orig.iterrows():
                    print(f"         {reg['CREATEDON']} - UserHistId: {reg['USERHISTID']}")
                    print(f"            MSISDN: {reg['MSISDN']}")
                    print(f"            BANKDOMAIN: {reg['BANKDOMAIN']}")
                    print(f"            USER_ID: {reg['USER_ID']}")
                    print(f"            O_USER_ID: {reg['O_USER_ID']}")
                    print()
            
        except Exception as e:
            print(f"      ❌ Error: {e}")

def proponer_solucion_procesamiento():
    """Propone solución para el procesamiento posterior"""
    print(f"\n4️⃣ PROPUESTA DE SOLUCIÓN:")
    
    print(f"   💡 El problema está en el procesamiento posterior (procesar.py)")
    print(f"   🔧 Posibles causas:")
    print(f"      1. Procesamiento usa diferente clave de agrupación")
    print(f"      2. Aplica diferente criterio de ordenamiento")
    print(f"      3. Tiene lógica adicional de filtrado")
    print(f"      4. Procesa desde LOG_USR.parquet en lugar de LOG_USR_DEDUPLICATED.parquet")
    
    print(f"\n   🎯 SOLUCIONES:")
    print(f"      1. VERIFICAR ORIGEN DE DATOS en procesar.py")
    print(f"         - ¿Usa LOG_USR.parquet o LOG_USR_DEDUPLICATED.parquet?")
    
    print(f"      2. REVISAR LÓGICA DE AGRUPACIÓN en procesar.py")
    print(f"         - ¿Qué campos usa para agrupar?")
    print(f"         - ¿Qué criterio usa para seleccionar registros?")
    
    print(f"      3. MODIFICAR procesar.py")
    print(f"         - Asegurar que use el mismo criterio que Oracle")
    print(f"         - Tomar el registro más reciente cuando hay duplicados")
    
    print(f"      4. VERIFICAR ORDEN DE PROCESAMIENTO")
    print(f"         - Asegurar que mantenga el orden correcto")

if __name__ == "__main__":
    print("🕵️ INVESTIGACIÓN DEL PROCESAMIENTO POSTERIOR")
    print("=" * 70)
    
    investigar_procesamiento_posterior()
    proponer_solucion_procesamiento()
    
    print(f"\n📋 CONCLUSIÓN:")
    print(f"   La deduplicación funciona correctamente (toma 09:41:55)")
    print(f"   El problema está en el procesamiento posterior que cambia a 00:52:48")
    print(f"   Necesitamos revisar y corregir la lógica de procesar.py")
    
    print(f"\n✅ Investigación completada")
