
  CREATE OR REPLACE EDITIONABLE PROCEDURE "USR_DATALAKE"."SP_ID_LOG_USR" (
    PARAM_FECHA IN VARCHAR,
    OUT_RESULTADO OUT VARCHAR
)
IS
    v_count INTEGER;  -- Variable para contar los registros
    v_timestamp_actual NUMBER;
    v_random_number NUMBER;
    v_generated_now NUMBER;  -- Identificador generado en el presente
    v_generated_ref NUMBER;  -- Semilla generada (v_generated_ref)
    v_difference NUMBER;     -- Diferencia entre los identificadores
BEGIN
    OUT_RESULTADO := 'Procesamiento iniciado';

    -- Verificamos si existen registros que procesar
    SELECT COUNT(*) 
    INTO v_count
    FROM USR_DATALAKE.LOG_USR
    WHERE TRUNC(CREATEDON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS')) 
    AND USERHISTID IS NULL;

    IF v_count = 0 THEN
        OUT_RESULTADO := 'No se encontraron registros para procesar';
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;

    -- Generación del identificador basado en la fecha proporcionada (semilla)
    -- Generamos el identificador de referencia (semilla) basado en "2025-03-15 00:00:00.000001"
    v_generated_ref := EXTRACT(SECOND FROM (TO_TIMESTAMP('2025-03-15 00:00:00.000001', 'YYYY-MM-DD HH24:MI:SS.FF') - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000000;  -- Milisegundos

    -- Proceso de actualización de los registros
    DECLARE
        CURSOR c_trans IS  
            SELECT ROWID 
            FROM USR_DATALAKE.LOG_USR
            WHERE TRUNC(CREATEDON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS')) 
            AND USERHISTID IS NULL
            FOR UPDATE;  -- Bloqueamos las filas para evitar problemas de concurrencia
        
        --counter INT := 0;
    BEGIN
        FOR r IN c_trans LOOP
            -- Convertimos la fecha actual a timestamp con mayor precisión (nanosegundos)
            v_timestamp_actual := EXTRACT(SECOND FROM (SYSTIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000000000;  -- Convertimos el timestamp actual en nanosegundos

            -- Generamos un número aleatorio entre 0 y 9
            v_random_number := FLOOR(DBMS_RANDOM.VALUE(0, 10));  -- Número aleatorio entre 0 y 9

            -- Concatenamos el timestamp y el número aleatorio para formar el identificador
            v_generated_now := v_timestamp_actual + v_random_number;

            DBMS_OUTPUT.PUT_LINE('v_generated_ref: ' || v_generated_ref);  -- Imprimir el valor de v_generated_ref
            DBMS_OUTPUT.PUT_LINE('v_generated_now: ' || v_generated_now);  -- Imprimir el valor de v_generated_now

            -- Calculamos la diferencia entre el identificador generado en el presente y el de referencia
            v_difference := v_generated_now - v_generated_ref;

            DBMS_OUTPUT.PUT_LINE('v_difference: ' || v_difference);  -- Imprimir la diferencia calculada

            -- Actualizamos el campo USERHISTID con la diferencia calculada
            UPDATE USR_DATALAKE.LOG_USR  
            SET USERHISTID = LPAD(TO_CHAR(v_difference),10,'0')
            WHERE ROWID = r.ROWID;

            /*counter := counter + 1;

            -- Realizamos un COMMIT cada 15000 registros
            IF counter >= 15000 THEN
                COMMIT;
                counter := 0;
            END IF;
			*/
            
            DBMS_LOCK.SLEEP(0.005);  -- Espera de 5ms para evitar problemas de concurrencia
        END LOOP;
		
        COMMIT;  -- Confirmamos todas las actualizaciones al final
        OUT_RESULTADO := 'Registros procesados correctamente';
    END;

EXCEPTION
    -- Manejo de excepciones en caso de errores
    WHEN OTHERS THEN
        OUT_RESULTADO := 'Error en el procesamiento: ' || SQLERRM;
        ROLLBACK;  -- En caso de error, hacemos un rollback de la transacción
END SP_ID_LOG_USR;

