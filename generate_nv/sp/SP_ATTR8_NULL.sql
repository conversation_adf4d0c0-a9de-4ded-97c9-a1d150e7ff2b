
  CREATE OR REPLACE EDITIONABLE PROCEDURE "USR_DATALAKE"."SP_ATTR8_NULL" (
    PARAM_FECHA IN VARCHAR,
    OUT_RESULTADO OUT VARCHAR -- Parámetro de salida
)
IS
    v_count INTEGER;
    v_timestamp_actual NUMBER(38);  -- Usar NUMBER para manejar valores grandes
    v_random_number NUMBER(1);  -- Solo 1 dígito para el número aleatorio (0-9)
    v_generated_now NUMBER(38);  -- Usamos NUMBER(38) para asegurarnos de que no haya desbordamientos
    v_generated_ref NUMBER(38);  -- Usamos NUMBER(38) para la referencia (semilla)
    v_difference NUMBER(38);  -- Usamos NUMBER(38) para la diferencia también
BEGIN
    -- Inicializar el mensaje de salida
    OUT_RESULTADO := 'Procesamiento iniciado';
    DBMS_OUTPUT.PUT_LINE('Iniciado el procesamiento...');

    -- Verificar si existen filas que cumplen con la condición
    SELECT COUNT(*) 
    INTO v_count
    FROM PDP_PROD10_MAINDB.USER_PROFILE
    WHERE TRUNC(CREATED_ON) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'))  
    AND ATTR8 IS NULL;
    
    DBMS_OUTPUT.PUT_LINE('v_count: ' || v_count);  -- Imprimir la cantidad de registros

    -- Si no hay filas que cumplan con la condición, salir del procedimiento
    IF v_count = 0 THEN
        OUT_RESULTADO := 'No se encontraron registros para procesar';
        DBMS_OUTPUT.PUT_LINE('No se encontraron registros para procesar');
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;

    -- Generación del identificador basado en la fecha proporcionada (semilla)
    -- Generamos el identificador de referencia (semilla) basado en "2025-03-15 00:00:00.000001"
    v_generated_ref := EXTRACT(SECOND FROM (TO_TIMESTAMP('2025-03-15 00:00:00.000001', 'YYYY-MM-DD HH24:MI:SS.FF') - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000000;  -- Milisegundos

    DBMS_OUTPUT.PUT_LINE('v_generated_ref: ' || v_generated_ref);  -- Imprimir el valor de v_generated_ref

    -- Proceso de actualización de los registros
    FOR r IN (
        SELECT ROWID
        FROM PDP_PROD10_MAINDB.USER_PROFILE
        WHERE TRUNC(CREATED_ON) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'))  
        AND ATTR8 IS NULL
        FOR UPDATE -- Bloqueamos las filas para evitar problemas de concurrencia
    ) LOOP
        -- Convertimos la fecha actual a timestamp con mayor precisión (nanosegundos)
        v_timestamp_actual := EXTRACT(SECOND FROM (SYSTIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000000000;  -- Convertir a nanosegundos

        -- Generamos un número aleatorio de 1 dígito (0-9)
        v_random_number := FLOOR(DBMS_RANDOM.VALUE(0, 10));  -- Número aleatorio entre 0 y 9

        -- Concatenamos el timestamp y el número aleatorio para formar el identificador de 12 dígitos
        v_generated_now := v_timestamp_actual + v_random_number;

        DBMS_OUTPUT.PUT_LINE('v_generated_now: ' || v_generated_now);  -- Imprimir el valor de v_generated_now

        -- Calculamos la diferencia entre el identificador generado en el presente y el de referencia
        v_difference := v_generated_now - v_generated_ref;

        DBMS_OUTPUT.PUT_LINE('v_difference: ' || v_difference);  -- Imprimir la diferencia calculada

        -- Actualización de ATTR8 con la diferencia calculada
        UPDATE PDP_PROD10_MAINDB.USER_PROFILE
        SET ATTR8 = LPAD(TO_CHAR(v_difference),10,'0')
        WHERE ROWID = r.ROWID;

        -- Espera de 5ms para evitar problemas de concurrencia
        DBMS_LOCK.SLEEP(0.005);  
    END LOOP;

    -- Confirmar todas las actualizaciones
    COMMIT;

    -- Mensaje de salida cuando los registros se procesan correctamente
    OUT_RESULTADO := 'Registros procesados correctamente';
    DBMS_OUTPUT.PUT_LINE('Registros procesados correctamente');

EXCEPTION
    -- Manejo de excepciones en caso de errores
    WHEN OTHERS THEN
        OUT_RESULTADO := 'Error en el procesamiento: ' || SQLERRM;
        DBMS_OUTPUT.PUT_LINE('Error en el procesamiento: ' || SQLERRM);
        ROLLBACK;  -- En caso de error, hacemos un rollback de la transacción
END SP_ATTR8_NULL;

