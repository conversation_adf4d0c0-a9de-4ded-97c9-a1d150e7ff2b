
  CREATE OR R<PERSON>LACE EDITIONABLE PROCEDURE "USR_<PERSON><PERSON><PERSON><PERSON><PERSON>"."SP_LOG_USR" (PARAM_FECHA IN VARCHAR, OUT_RESULTADO OUT VARCHAR -- <PERSON>r<PERSON><PERSON><PERSON> de <PERSON>lid<PERSON>
)
IS
    v_count INTEGER;
BEGIN
---------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY - LOG - USUARIOS  ------------------------------------------------
	
	--DELETE FROM USR_DATALAKE.LOG_USR WHERE TRUNC(createdOn) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'));

	SELECT COUNT(*) 
    INTO v_count
    FROM USR_DATALAKE.LOG_USR
    WHERE TRUNC(CREATEDON) = TRUNC(TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS'));
    
    DBMS_OUTPUT.PUT_LINE('v_count: ' || v_count);  -- Imprimir la cantidad de registros

    -- Si no hay filas que cumplan con la condición, salir del procedimiento
    IF v_count <> 0 THEN
        OUT_RESULTADO := 'El procedimiento ya ha sido procesado';
        DBMS_OUTPUT.PUT_LINE('El SP ya ha sido procesado');
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;

	EXECUTE IMMEDIATE 'DELETE FROM USR_DATALAKE.LOG_USR WHERE TRUNC(createdOn) = (SELECT TRUNC(MAX(createdOn)) FROM USR_DATALAKE.LOG_USR) -31';
	
	INSERT INTO USR_DATALAKE.LOG_USR 
	WITH
	WALLET_OLD AS (
	SELECT 
		UD.USER_ID,
		UD.ATTR7_OLD,
		UD.ATTR8_OLD,
		UD.CREATED_AT,
		ID.ISSUER_CODE,
		UD.GRADE_OLD AS GRADE_NAME_OLD,
		ROW_NUMBER() OVER(PARTITION BY UD.USER_ID ORDER BY UD.CREATED_AT DESC) AS ORDEN
	FROM USR_DATALAKE.USER_ACCOUNT_HISTORY UD 
	INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON UD.ISSUER_OLD = ID.ISSUER_ID
	),
	PROCESS AS (
		SELECT 
		'UM.' || to_char(rownum) AS userHistId, 
		umh.created_on AS createdOn,
		ud.ID_TYPE AS TipoDocumento,
		ud.ID_VALUE AS Documento,
		ud.MSISDN AS Msisdn,
		NULL AS MsisdnB,
		ud.ISSUER_CODE AS BankDomain,
		CASE 
			WHEN UMH.REQUEST_TYPE IN ('Resume User','Unlock Wallet') THEN 'ID:awspdp/ADMIN'
			ELSE REPLACE(REPLACE(umh.created_by,'US.',''),'SELF','ID:unknown/SERVICE') 
		END AS created_by,
		ud.USER_ID_M AS userId,
		'MOBILE_MONEY' AS accountType,
		ud.WALLET_NUMBER AS accountId,
		ud.FIRST_NAME AS Nombre,
		ud.LAST_NAME AS Apellido,
		NULL AS NNombre,
		NULL AS NApellido,
		REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
		NULL AS perfilB,
		ud.PREFERRED_LANG AS IdiomaA,
		NULL AS IdiomaB,
		ud.ATTR1 AS TelcoA,
		NULL AS TelcoB,
		umh.razon,
		ud.grade_name as PerfilCuenta,
		ud.grade_name as PerfilCuentaA,
		NULL AS perfilCuentaB,
		ud.ID_TYPE AS TipoDocumentoA,
		NULL AS TipoDocumentoB,
		ud.ID_VALUE AS DocumentoB,
		NULL AS NumDocumentoB,
		umh.request_type AS requestType, 
		umh.old_data AS oldData, 
		umh.new_data AS newData,
		UD.O_USER_ID
	FROM USR_DATALAKE.USER_MODIFICATION_DAY umh
	INNER JOIN USR_DATALAKE.USER_DATA_TRX ud ON umh.user_id = ud.O_USER_ID
	
	UNION ALL
	
	SELECT
		uach.AUTHENTICATION_ID AS userHistId,
		uach.MODIFIED_ON AS createdOn,
		ud.ID_TYPE AS TipoDocumento,
		ud.ID_VALUE AS Documento,
		ud.MSISDN AS Msisdn,
		NULL AS MsisdnB,
		ud.ISSUER_CODE AS BankDomain,
		CASE 
			WHEN UACH.MODIFICATION_TYPE = 'RESET_AUTH_VALUE' THEN 'ID:unknown/SERVICE'
			ELSE REPLACE(REPLACE(uach.MODIFIED_BY,'US.',''),'SELF','ID:unknown/SERVICE') 
		END AS created_by,
		ud.USER_ID_M AS userId,
		'MOBILE_MONEY' AS accountType,
		ud.WALLET_NUMBER AS accountId,
		ud.FIRST_NAME AS Nombre,
		ud.LAST_NAME AS Apellido,
		NULL AS NNombre,
		NULL AS NApellido,
		REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
		NULL AS perfilB,
		ud.PREFERRED_LANG AS IdiomaA,
		NULL AS IdiomaB,
		ud.ATTR1 AS TelcoA,
		NULL AS TelcoB,
		NULL AS Razon,
		ud.grade_name as PerfilCuenta,
		ud.grade_name as PerfilCuentaA,
		NULL AS perfilCuentaB,
		ud.ID_TYPE AS TipoDocumentoA,
		NULL AS TipoDocumentoB,
		ud.ID_VALUE AS DocumentoB,
		NULL AS NumDocumentoB,
		uach.MODIFICATION_TYPE  AS requestType,
		NULL AS oldData, 
		NULL AS newData,
		UD.O_USER_ID
	FROM USR_DATALAKE.USER_AUTH_CHANGE_HISTORY uach  
	INNER JOIN PDP_PROD10_MAINDB.USER_IDENTIFIER ui  ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID  
	INNER JOIN USR_DATALAKE.USER_DATA_TRX ud ON ui.USER_ID = ud.O_USER_ID
	
	
	UNION ALL
	
	SELECT 
		ud.O_USER_ID AS userHistId,
	    ud.CREATED_ON  AS createdOn,
	    CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_TYPE END AS TipoDocumento,
	    CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_VALUE END AS Documento,
	    ud.MSISDN AS Msisdn,
	    NULL AS MsisdnB,
		ud.ISSUER_CODE AS BankDomain,
		REPLACE(REPLACE(ud.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE')  AS created_by,
		ud.USER_ID_M AS userId,
		'MOBILE_MONEY' AS accountType,
		ud.WALLET_NUMBER AS accountId,
		ud.FIRST_NAME AS Nombre,
		ud.LAST_NAME AS Apellido,
		NULL AS NNombre,
		NULL AS NApellido,
		REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
		NULL AS perfilB,
		ud.PREFERRED_LANG AS IdiomaA,
		NULL AS IdiomaB,
		ud.ATTR1 AS TelcoA,
		NULL AS TelcoB,
		NULL AS Razon,
		CASE WHEN ud.grade_name LIKE '%GENERAL%' THEN REPLACE(ud.grade_name,'NORMAL GENERAL','NORMAL') ELSE ud.grade_name END as PerfilCuenta,
		CASE WHEN ud.grade_name LIKE '%GENERAL%' THEN REPLACE(ud.grade_name,'NORMAL GENERAL','NORMAL') ELSE ud.grade_name END as PerfilCuentaA,
		NULL AS perfilCuentaB,
		ud.ID_TYPE AS TipoDocumentoA,
		NULL AS TipoDocumentoB,
		ud.ID_VALUE AS DocumentoB,
		NULL AS NumDocumentoB,
		req.requestType  AS requestType,
		NULL AS oldData, 
		NULL AS newData,
		UD.O_USER_ID
	FROM USR_DATALAKE.USER_DATA_TRX ud 
	CROSS JOIN (SELECT 'ActivateUser' AS requestType FROM dual
	            UNION ALL 
	            SELECT 'AfiliaUser' FROM dual) req
	WHERE 1=1
	AND trunc(ud.CREATED_ON)  = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'))
	UNION ALL
	
	SELECT 
		ud.O_USER_ID AS userHistId,
	    ud.STATUS_CHANGE_ON  AS createdOn,
	    ud.ID_TYPE AS TipoDocumento,
	    ud.ID_VALUE || 'X' || ud.USER_ID_M  AS Documento,
	    ud.MSISDN AS Msisdn,
	    NULL AS MsisdnB,
		ud.ISSUER_CODE AS BankDomain,
		REPLACE(REPLACE(ud.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE')  AS created_by,
		ud.USER_ID_M AS userId,
		'MOBILE_MONEY' AS accountType,
		ud.WALLET_NUMBER AS accountId,
		ud.FIRST_NAME AS Nombre,
		ud.LAST_NAME AS Apellido,
		NULL AS NNombre,
		NULL AS NApellido,
		REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
		NULL AS perfilB,
		ud.PREFERRED_LANG AS IdiomaA,
		NULL AS IdiomaB,
		ud.ATTR1 AS TelcoA,
		NULL AS TelcoB,
		ud.remarks AS Razon,
		ud.grade_name as PerfilCuenta,
		ud.grade_name as PerfilCuentaA,
		NULL AS perfilCuentaB,
		ud.ID_TYPE AS TipoDocumentoA,
		NULL AS TipoDocumentoB,
		ud.ID_VALUE AS DocumentoB,
		NULL AS NumDocumentoB,
		req.requestType AS requestType,
	    NULL AS oldData, 
	    NULL AS newData,
	    UD.O_USER_ID
	FROM USR_DATALAKE.USER_DATA_TRX ud
	CROSS JOIN (SELECT 'ClosedAccount' AS requestType FROM dual
	            UNION ALL 
	            SELECT 'ClosedUserAccount' FROM dual) req
	WHERE ud.STATUS = 'N'
	AND trunc(ud.STATUS_CHANGE_ON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'))
	)
	SELECT 
		userHistId AS userHistId,
		P.createdOn,
		P.TipoDocumento,
		P.Documento,
		P.Msisdn,
		P.MsisdnB,
		CASE 
			WHEN DA.USER_ID IS NOT NULL THEN DA.ISSUER_CODE
			ELSE P.BankDomain
		END AS BankDomain,
		P.created_by,
		CASE 
			WHEN DA.USER_ID IS NOT NULL THEN DA.attr7_old
			ELSE P.userId 
		END AS userId,
		P.accountType,
		CASE 
			WHEN DA.USER_ID IS NOT NULL THEN DA.attr8_old
			ELSE P.accountId
		END AS accountId,
		P.Nombre,
		P.Apellido,
		P.NNombre,
		P.NApellido,
		CASE 
			WHEN DA.USER_ID IS NOT NULL THEN DA.ISSUER_CODE || ' ' || SUBSTR(P.perfilA,INSTR(P.perfilA,' ',1)) 
			ELSE P.perfilA 
		END AS perfilA,
		P.perfilB,
		P.IdiomaA,
		P.IdiomaB,
		P.TelcoA,
		P.TelcoB,
		P.Razon,
		CASE 
			WHEN DA.USER_ID IS NOT NULL THEN DA.ISSUER_CODE || ' ' || UPPER(DA.GRADE_NAME_OLD)
			ELSE P.PerfilCuenta
		END AS PerfilCuenta,
		CASE 
			WHEN DA.USER_ID IS NOT NULL THEN DA.ISSUER_CODE || ' ' || UPPER(DA.GRADE_NAME_OLD)
			ELSE P.PerfilCuentaA
		END AS PerfilCuentaA,
		P.perfilCuentaB,
		P.TipoDocumentoA,
		P.TipoDocumentoB,
		P.DocumentoB,
		P.NumDocumentoB,
		P.requestType,
		P.oldData,
		P.newData,
		DA2.ATTR7_OLD AS userIdOld,
		DA2.ATTR8_OLD AS accountIdOld
	FROM PROCESS P
	LEFT JOIN WALLET_OLD DA ON P.O_USER_ID = DA.USER_ID AND TO_CHAR(P.createdOn,'YYYY-MM-DD HH24:MI') < TO_CHAR(DA.created_at,'YYYY-MM-DD HH24:MI')
	LEFT JOIN WALLET_OLD DA2 ON P.O_USER_ID = DA2.USER_ID;
	COMMIT;

END SP_LOG_USR;

