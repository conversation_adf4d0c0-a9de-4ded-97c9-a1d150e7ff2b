
  CREATE OR <PERSON><PERSON><PERSON>CE EDITIONABLE PROCEDURE "USR_<PERSON><PERSON><PERSON><PERSON><PERSON>"."USER_MODIFICATION" (PARAM_FECHA IN VARCHAR)
IS 
BEGIN
---------------------------------------------------------------------------------------------------------------	
-----------------------------------------QUERY - LOG - USUARIOS  ------------------------------------------------
	
	EXECUTE IMMEDIATE 'TRUNCATE TABLE USR_DATALAKE.USER_MODIFICATION_DAY';

	INSERT INTO USR_DATALAKE.USER_MODIFICATION_DAY
	SELECT *
	FROM PDP_PROD10_MAINDB.USER_MODIFICATION_HISTORY umh
	WHERE trunc(umh.CREATED_ON) = TRUNC(TO_DATE(PARAM_FECHA,'YYYY-MM-DD HH24:MI:SS'));

	COMMIT;

END USER_MODIFICATION;

