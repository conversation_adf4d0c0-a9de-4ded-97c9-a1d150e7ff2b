
  CREATE OR REPLACE EDITIONABLE PROCEDURE "USR_DAT<PERSON>AKE"."SP_FIELD7" (
    PARAM_FECHA IN VARCHAR,
    OUT_RESULTADO OUT VARCHAR
)
IS
    v_count INTEGER;         -- Diferencia entre identificadores
BEGIN
    -- Inicializamos el resultado
    OUT_RESULTADO := 'Procesamiento iniciado';

    -- Verificar si existen filas que cumplen con la condición
    SELECT COUNT(*) 
    INTO v_count
    FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
    WHERE TRANSFER_DATE >= TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS')
	AND TRANSFER_DATE < TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS')+1
    AND FIELD7 IS NULL;
   
    IF v_count = 0 THEN
        OUT_RESULTADO := 'No se encontraron registros para procesar';
        RETURN;  -- No hacer nada si no hay filas que procesar
    END IF;

    -- Generación del identificador basado en la fecha proporcionada (semilla)
    -- Generamos el identificador de referencia (semilla) basado en "2025-03-15 00:00:00.000001"
    --v_generated_ref := EXTRACT(SECOND FROM (TO_TIMESTAMP('2025-03-15 00:00:00.000001', 'YYYY-MM-DD HH24:MI:SS.FF') - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000000;  -- Milisegundos

    -- Proceso de actualización de los registros
    FOR r IN (
        SELECT ROWID
        FROM PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER
        WHERE TRANSFER_DATE >= TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS')
	AND TRANSFER_DATE < TO_DATE(PARAM_FECHA, 'YYYY-MM-DD HH24:MI:SS')+1
        AND FIELD7 IS NULL
        FOR UPDATE -- Bloqueamos las filas para evitar problemas de concurrencia
    ) LOOP  
        
       	UPDATE PDP_PROD10_MAINDBBUS.MTX_TRANSACTION_HEADER  
        SET FIELD7 = TO_CHAR(FLOOR((SYSDATE - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400000 +  
                         MOD(DBMS_UTILITY.GET_TIME, 1000)))  
                         || LPAD(TO_CHAR(FLOOR(DBMS_RANDOM.VALUE(0, 100))), 2, '0')  
        WHERE ROWID = r.ROWID;

        DBMS_LOCK.SLEEP(0.005);  -- Espera de 5ms para evitar problemas de concurrencia
    END LOOP;

    COMMIT;  -- Confirmamos todas las actualizaciones al final

    OUT_RESULTADO := 'Registros procesados correctamente';

EXCEPTION
    -- Manejo de excepciones en caso de errores
    WHEN OTHERS THEN
        OUT_RESULTADO := 'Error en el procesamiento: ' || SQLERRM;
        ROLLBACK;  -- En caso de error, hacemos un rollback de la transacción

END SP_FIELD7;

