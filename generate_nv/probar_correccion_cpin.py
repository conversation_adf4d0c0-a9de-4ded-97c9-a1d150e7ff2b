#!/usr/bin/env python3
"""
Script para probar la corrección del pipeline y verificar que se generen los registros CPIN
"""
import subprocess
import pandas as pd
import os
from datetime import datetime

def probar_correccion_pipeline():
    """Prueba la corrección del pipeline"""
    print("🔧 PRUEBA DE CORRECCIÓN DEL PIPELINE CPIN")
    print("=" * 55)
    
    fecha_prueba = "2025-06-09"
    
    print(f"📅 Fecha de prueba: {fecha_prueba}")
    print(f"🎯 Objetivo: Verificar que se genere el registro CPIN faltante")
    print(f"   • Documento: 71793435")
    print(f"   • Celular: 51907368782")
    print(f"   • Hora esperada: 22:52:32")
    
    # Ejecutar pipeline corregido
    print(f"\n🚀 EJECUTANDO PIPELINE CORREGIDO...")
    
    try:
        # Ejecutar el pipeline
        resultado = subprocess.run([
            'python3', 'pipeline_log_usuarios_duckdb.py', fecha_prueba
        ], capture_output=True, text=True, timeout=300)
        
        if resultado.returncode == 0:
            print(f"✅ Pipeline ejecutado exitosamente")
            print(f"📋 Salida del pipeline:")
            
            # Mostrar las últimas líneas del log
            lineas_salida = resultado.stdout.split('\n')
            for linea in lineas_salida[-20:]:
                if linea.strip():
                    print(f"   {linea}")
        else:
            print(f"❌ Error en pipeline:")
            print(f"   {resultado.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Pipeline tardó más de 5 minutos - continuando con verificación")
    except Exception as e:
        print(f"❌ Error ejecutando pipeline: {e}")
        return False
    
    # Verificar resultados
    print(f"\n🔍 VERIFICANDO RESULTADOS...")
    return verificar_resultados_correccion(fecha_prueba)

def verificar_resultados_correccion(fecha):
    """Verifica si la corrección funcionó"""
    
    # Verificar archivo final
    archivo_final = f"output/{fecha.replace('-', '')}/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-{fecha.replace('-', '')}*.csv"
    
    # Buscar archivos que coincidan con el patrón
    import glob
    archivos_finales = glob.glob(archivo_final)
    
    if not archivos_finales:
        print(f"❌ No se encontró archivo final: {archivo_final}")
        return False
    
    archivo_final = archivos_finales[0]
    print(f"📄 Analizando archivo final: {os.path.basename(archivo_final)}")
    
    try:
        df = pd.read_csv(archivo_final, header=None)
        
        # Asignar nombres de columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        print(f"📊 Estadísticas del archivo corregido:")
        print(f"   • Total registros: {len(df):,}")
        
        # Contar operaciones CPIN
        cpin_total = len(df[df['OPERACION'] == 'CPIN'])
        print(f"   • Total CPIN: {cpin_total:,}")
        
        # Buscar nuestro usuario específico
        documento = "71793435"
        celular = "51907368782"
        
        mask_usuario = (df['DOCUMENTO'].astype(str) == documento) & \
                      (df['CELULAR'].astype(str) == celular)
        
        registros_usuario = df[mask_usuario]
        print(f"   • Registros del usuario: {len(registros_usuario)}")
        
        if len(registros_usuario) > 0:
            print(f"   📋 Operaciones del usuario:")
            operaciones = registros_usuario['OPERACION'].value_counts()
            for op, count in operaciones.items():
                print(f"      {op}: {count}")
            
            # Verificar si ahora tiene CPIN
            cpin_usuario = registros_usuario[registros_usuario['OPERACION'] == 'CPIN']
            
            if len(cpin_usuario) > 0:
                print(f"   ✅ ¡ÉXITO! Usuario ahora tiene {len(cpin_usuario)} operación(es) CPIN")
                
                for _, reg in cpin_usuario.iterrows():
                    print(f"      🎯 CPIN: {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']}")
                    
                    # Verificar si es el registro específico que buscábamos
                    if "22:52" in str(reg['FECHA_HORA']):
                        print(f"      🏆 ¡REGISTRO FALTANTE ENCONTRADO!")
                        print(f"         Fecha/Hora: {reg['FECHA_HORA']}")
                        print(f"         TransactionID: {reg['TRANSACTIONID']}")
                        return True
                
                print(f"   ⚠️  Se generaron registros CPIN pero no el específico de 22:52:32")
                return True  # Parcialmente exitoso
            else:
                print(f"   ❌ Usuario aún NO tiene operaciones CPIN")
                return False
        else:
            print(f"   ❌ Usuario no encontrado en archivo final")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando resultados: {e}")
        return False

def comparar_con_original():
    """Compara los resultados con el archivo original"""
    print(f"\n📊 COMPARACIÓN CON ARCHIVO ORIGINAL:")
    
    # Archivo original
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    # Archivo corregido (buscar el más reciente)
    import glob
    archivos_corregidos = glob.glob("output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-*.csv")
    
    if not archivos_corregidos:
        print(f"❌ No se encontró archivo corregido")
        return
    
    archivo_corregido = max(archivos_corregidos, key=os.path.getctime)
    
    try:
        # Leer archivos
        df_original = pd.read_csv(archivo_original, header=None)
        df_corregido = pd.read_csv(archivo_corregido, header=None)
        
        print(f"📊 Comparación de conteos:")
        print(f"   • Original: {len(df_original):,} registros")
        print(f"   • Corregido: {len(df_corregido):,} registros")
        print(f"   • Diferencia: {len(df_corregido) - len(df_original):+,} registros")
        
        # Comparar operaciones CPIN
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_original.columns = columnas[:len(df_original.columns)]
        df_corregido.columns = columnas[:len(df_corregido.columns)]
        
        cpin_original = len(df_original[df_original['OPERACION'] == 'CPIN'])
        cpin_corregido = len(df_corregido[df_corregido['OPERACION'] == 'CPIN'])
        
        print(f"📊 Comparación CPIN:")
        print(f"   • Original: {cpin_original:,} registros CPIN")
        print(f"   • Corregido: {cpin_corregido:,} registros CPIN")
        print(f"   • Diferencia: {cpin_corregido - cpin_original:+,} registros CPIN")
        
        if cpin_corregido == cpin_original:
            print(f"   🎯 ¡COINCIDENCIA EXACTA EN CPIN!")
        elif cpin_corregido > cpin_original:
            print(f"   ✅ Mejora: Se agregaron registros CPIN")
        else:
            print(f"   ❌ Problema: Se perdieron registros CPIN")
            
    except Exception as e:
        print(f"❌ Error en comparación: {e}")

def generar_reporte_final():
    """Genera reporte final de la corrección"""
    print(f"\n📋 REPORTE FINAL DE CORRECCIÓN:")
    print("=" * 50)
    
    print(f"🔧 CAMBIOS REALIZADOS:")
    print(f"   1. ✅ Agregado filtro WHERE en JOIN de USER_AUTH_CHANGE_HISTORY")
    print(f"   2. ✅ Agregado logging para verificar registros CHANGE_AUTH_FACTOR")
    print(f"   3. ✅ Mejorada lógica de JOIN para evitar registros nulos")
    
    print(f"\n🎯 OBJETIVO:")
    print(f"   • Generar registro CPIN faltante para documento 71793435")
    print(f"   • Fecha/Hora: 2025-06-09 22:52:32")
    print(f"   • Lograr coincidencia exacta con flujo original")
    
    print(f"\n📊 RESULTADO ESPERADO:")
    print(f"   • Archivo final debe tener 12,942 registros (igual que original)")
    print(f"   • Debe incluir el registro CPIN faltante")
    print(f"   • Coincidencia 100% con flujo original")

if __name__ == "__main__":
    print("🧪 PRUEBA DE CORRECCIÓN DEL PIPELINE CPIN")
    print("=" * 60)
    
    # Ejecutar prueba
    exito = probar_correccion_pipeline()
    
    # Comparar con original
    comparar_con_original()
    
    # Generar reporte
    generar_reporte_final()
    
    if exito:
        print(f"\n🏆 ¡CORRECCIÓN EXITOSA!")
        print(f"✅ El pipeline ahora genera correctamente los registros CPIN")
    else:
        print(f"\n❌ CORRECCIÓN NECESITA AJUSTES")
        print(f"🔍 Revisar logs del pipeline para más detalles")
    
    print(f"\n✅ Prueba completada")
