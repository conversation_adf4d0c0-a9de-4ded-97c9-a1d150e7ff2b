#!/usr/bin/env python3
"""
🕵️‍♂️ DETECTIVE EXTREMO: Investigación profunda de discrepancia Oracle vs S3
"""
import pandas as pd
import os
import glob
from datetime import datetime, timed<PERSON><PERSON>

def detective_extremo_investigation():
    """Investigación forense completa de la discrepancia Oracle vs S3"""
    print("🕵️‍♂️ DETECTIVE EXTREMO - INVESTIGACIÓN FORENSE")
    print("=" * 60)
    print("🎯 CASO: Registro CHANGE_AUTH_FACTOR faltante en S3")
    print("📅 FECHA: 2025-06-09")
    print("🕐 HORA: 22:52:32")
    print("👤 SOSPECHOSO: USER_ID con documento 71793435")
    
    # FASE 1: Aná<PERSON>is forense de archivos temporales
    print(f"\n🔍 FASE 1: ANÁLISIS FORENSE DE ARCHIVOS TEMPORALES")
    analisis_forense_temporales()
    
    # FASE 2: Investigación de configuración S3
    print(f"\n🔍 FASE 2: INVESTIGACIÓN DE CONFIGURACIÓN S3")
    investigar_configuracion_s3()
    
    # FASE 3: Análisis de patrones temporales
    print(f"\n🔍 FASE 3: ANÁLISIS DE PATRONES TEMPORALES")
    analizar_patrones_temporales()
    
    # FASE 4: Investigación de filtros ocultos
    print(f"\n🔍 FASE 4: BÚSQUEDA DE FILTROS OCULTOS")
    buscar_filtros_ocultos()
    
    # FASE 5: Análisis comparativo de tipos de modificación
    print(f"\n🔍 FASE 5: ANÁLISIS COMPARATIVO DE TIPOS")
    analizar_tipos_modificacion()
    
    # FASE 6: Investigación de logs del sistema
    print(f"\n🔍 FASE 6: INVESTIGACIÓN DE LOGS DEL SISTEMA")
    investigar_logs_sistema()

def analisis_forense_temporales():
    """Análisis forense detallado de archivos temporales"""
    print("   🔬 ANÁLISIS FORENSE DE ARCHIVOS TEMPORALES")
    
    # Investigar USER_MODIFICATION_DAY con lupa
    archivo_mod = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_MODIFICATION_DAY.parquet"
    
    if os.path.exists(archivo_mod):
        try:
            df = pd.read_parquet(archivo_mod)
            print(f"   📊 EVIDENCIA: USER_MODIFICATION_DAY.parquet")
            print(f"      🔢 Total registros: {len(df):,}")
            
            # Análisis temporal detallado
            if 'CREATED_ON' in df.columns:
                df['CREATED_ON'] = pd.to_datetime(df['CREATED_ON'])
                
                # Extraer horas
                df['HORA'] = df['CREATED_ON'].dt.hour
                df['MINUTO'] = df['CREATED_ON'].dt.minute
                
                # Análisis por hora
                print(f"      ⏰ DISTRIBUCIÓN TEMPORAL:")
                horas_dist = df['HORA'].value_counts().sort_index()
                for hora, count in horas_dist.items():
                    print(f"         {hora:02d}:XX - {count:,} registros")
                
                # Buscar registros cercanos a 22:52
                hora_22 = df[df['HORA'] == 22]
                print(f"      🎯 REGISTROS HORA 22:XX: {len(hora_22)}")
                
                if len(hora_22) > 0:
                    print(f"         📋 Detalle registros 22:XX:")
                    for _, reg in hora_22.iterrows():
                        print(f"            {reg['CREATED_ON']} - {reg['REQUEST_TYPE']} - User: {reg.get('USER_ID', 'N/A')}")
                
                # Buscar el último registro antes de 22:52
                antes_2252 = df[df['CREATED_ON'] < '2025-06-09 22:52:32']
                if len(antes_2252) > 0:
                    ultimo_antes = antes_2252.loc[antes_2252['CREATED_ON'].idxmax()]
                    print(f"      🕐 ÚLTIMO REGISTRO ANTES 22:52:32:")
                    print(f"         {ultimo_antes['CREATED_ON']} - {ultimo_antes['REQUEST_TYPE']}")
                
                # Buscar el primer registro después de 22:52
                despues_2252 = df[df['CREATED_ON'] > '2025-06-09 22:52:32']
                if len(despues_2252) > 0:
                    primero_despues = despues_2252.loc[despues_2252['CREATED_ON'].idxmin()]
                    print(f"      🕐 PRIMER REGISTRO DESPUÉS 22:52:32:")
                    print(f"         {primero_despues['CREATED_ON']} - {primero_despues['REQUEST_TYPE']}")
                else:
                    print(f"      ❌ NO HAY REGISTROS DESPUÉS DE 22:52:32")
                    print(f"      💡 PISTA: Posible corte temporal en extracción")
            
            # Análisis de tipos de REQUEST_TYPE
            print(f"      📊 ANÁLISIS DE REQUEST_TYPE:")
            tipos = df['REQUEST_TYPE'].value_counts()
            for tipo, count in tipos.items():
                print(f"         {tipo}: {count:,}")
            
            # Buscar patrones sospechosos
            print(f"      🔍 BÚSQUEDA DE PATRONES SOSPECHOSOS:")
            
            # ¿Hay algún CHANGE_AUTH_FACTOR en todo el archivo?
            change_auth = df[df['REQUEST_TYPE'].str.contains('CHANGE_AUTH', na=False)]
            print(f"         CHANGE_AUTH*: {len(change_auth)} registros")
            
            # ¿Hay algún AUTH en general?
            auth_related = df[df['REQUEST_TYPE'].str.contains('AUTH', na=False)]
            print(f"         *AUTH*: {len(auth_related)} registros")
            
            # ¿Hay algún FACTOR?
            factor_related = df[df['REQUEST_TYPE'].str.contains('FACTOR', na=False)]
            print(f"         *FACTOR*: {len(factor_related)} registros")
            
        except Exception as e:
            print(f"      ❌ ERROR EN ANÁLISIS: {e}")

def investigar_configuracion_s3():
    """Investigar la configuración de fuentes S3"""
    print("   🔧 INVESTIGACIÓN DE CONFIGURACIÓN S3")
    
    # Leer configuración S3
    config_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/config/s3_sources.yaml"
    
    if os.path.exists(config_file):
        print(f"   📄 EVIDENCIA: s3_sources.yaml")
        with open(config_file, 'r') as f:
            contenido = f.read()
        
        print(f"   🔍 CONFIGURACIÓN USER_MODIFICATION_HISTORY:")
        lineas = contenido.split('\n')
        en_user_mod = False
        
        for linea in lineas:
            if 'user_modification_history' in linea:
                en_user_mod = True
                print(f"      {linea}")
            elif en_user_mod and linea.strip() and not linea.startswith(' '):
                en_user_mod = False
            elif en_user_mod:
                print(f"      {linea}")
    
    # Verificar fuentes_s3.md
    fuentes_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/fuentes_s3.md"
    
    if os.path.exists(fuentes_file):
        print(f"   📄 EVIDENCIA: fuentes_s3.md")
        with open(fuentes_file, 'r') as f:
            contenido = f.read()
        
        if 'user_modification_history' in contenido.lower():
            print(f"   🔍 INFORMACIÓN USER_MODIFICATION_HISTORY:")
            lineas = contenido.split('\n')
            for i, linea in enumerate(lineas):
                if 'user_modification_history' in linea.lower():
                    # Mostrar contexto
                    inicio = max(0, i-2)
                    fin = min(len(lineas), i+3)
                    for j in range(inicio, fin):
                        print(f"      {lineas[j]}")
                    break

def analizar_patrones_temporales():
    """Análisis detallado de patrones temporales"""
    print("   ⏰ ANÁLISIS DE PATRONES TEMPORALES")
    
    # Comparar con archivo original
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    if os.path.exists(archivo_original):
        try:
            df_orig = pd.read_csv(archivo_original, header=None)
            columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                       'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
            df_orig.columns = columnas[:len(df_orig.columns)]
            
            print(f"   📊 ANÁLISIS TEMPORAL DEL ARCHIVO ORIGINAL:")
            
            # Convertir fechas
            df_orig['FECHA_HORA'] = pd.to_datetime(df_orig['FECHA_HORA'])
            df_orig['HORA'] = df_orig['FECHA_HORA'].dt.hour
            df_orig['MINUTO'] = df_orig['FECHA_HORA'].dt.minute
            
            # Buscar registros CPIN en hora 22
            cpin_22 = df_orig[(df_orig['OPERACION'] == 'CPIN') & (df_orig['HORA'] == 22)]
            print(f"      🎯 REGISTROS CPIN HORA 22:XX: {len(cpin_22)}")
            
            if len(cpin_22) > 0:
                print(f"      📋 DETALLE CPIN 22:XX:")
                for _, reg in cpin_22.iterrows():
                    print(f"         {reg['FECHA_HORA']} - TID: {reg['TRANSACTIONID']} - Doc: {reg['DOCUMENTO']}")
            
            # Análisis de distribución temporal de CPIN
            cpin_total = df_orig[df_orig['OPERACION'] == 'CPIN']
            print(f"      📊 DISTRIBUCIÓN TEMPORAL CPIN:")
            cpin_horas = cpin_total['HORA'].value_counts().sort_index()
            for hora, count in cpin_horas.items():
                print(f"         {hora:02d}:XX - {count:,} registros CPIN")
            
        except Exception as e:
            print(f"      ❌ ERROR: {e}")

def buscar_filtros_ocultos():
    """Buscar filtros ocultos que puedan estar excluyendo CHANGE_AUTH_FACTOR"""
    print("   🔍 BÚSQUEDA DE FILTROS OCULTOS")
    
    archivo_pipeline = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios_duckdb.py"
    
    if os.path.exists(archivo_pipeline):
        with open(archivo_pipeline, 'r') as f:
            contenido = f.read()
        
        # Buscar la consulta exacta de user_modification_history
        print(f"   🔍 CONSULTA USER_MODIFICATION_HISTORY:")
        
        lineas = contenido.split('\n')
        en_consulta = False
        consulta_lines = []
        
        for i, linea in enumerate(lineas):
            if 'user_modification_history' in linea and 'read_parquet' in linea:
                en_consulta = True
                consulta_lines.append((i+1, linea))
            elif en_consulta:
                if linea.strip() and ('"""' in linea or 'def ' in linea):
                    en_consulta = False
                else:
                    consulta_lines.append((i+1, linea))
                    if len(consulta_lines) > 15:  # Limitar
                        break
        
        if consulta_lines:
            print(f"      📄 CONSULTA EXACTA:")
            for linea_num, linea in consulta_lines:
                print(f"         {linea_num}: {linea}")
        
        # Buscar filtros específicos
        filtros_sospechosos = []
        for i, linea in enumerate(lineas):
            linea_upper = linea.upper()
            if any(palabra in linea_upper for palabra in ['MODIFICATION_TYPE', 'REQUEST_TYPE']) and \
               any(filtro in linea_upper for filtro in ['WHERE', 'AND', 'NOT', 'EXCLUDE', '!=']):
                filtros_sospechosos.append((i+1, linea.strip()))
        
        if filtros_sospechosos:
            print(f"      ⚠️  FILTROS SOSPECHOSOS ENCONTRADOS:")
            for linea_num, linea in filtros_sospechosos:
                print(f"         {linea_num}: {linea}")
        else:
            print(f"      ✅ No se encontraron filtros sospechosos")

def analizar_tipos_modificacion():
    """Análisis comparativo de tipos de modificación"""
    print("   📊 ANÁLISIS COMPARATIVO DE TIPOS DE MODIFICACIÓN")
    
    # Verificar si hay archivos de USER_AUTH_CHANGE_HISTORY
    archivo_auth = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_AUTH_CHANGE_HISTORY.parquet"
    
    if os.path.exists(archivo_auth):
        try:
            df_auth = pd.read_parquet(archivo_auth)
            print(f"   📄 EVIDENCIA: USER_AUTH_CHANGE_HISTORY.parquet")
            print(f"      🔢 Total registros: {len(df_auth):,}")
            print(f"      📋 Columnas: {list(df_auth.columns)}")
            
            if 'MODIFICATION_TYPE' in df_auth.columns:
                tipos_auth = df_auth['MODIFICATION_TYPE'].value_counts()
                print(f"      📊 TIPOS DE MODIFICATION_TYPE:")
                for tipo, count in tipos_auth.items():
                    print(f"         {tipo}: {count:,}")
                
                # Buscar CHANGE_AUTH_FACTOR específicamente
                change_auth = df_auth[df_auth['MODIFICATION_TYPE'] == 'CHANGE_AUTH_FACTOR']
                print(f"      🎯 CHANGE_AUTH_FACTOR: {len(change_auth)}")
                
                if len(change_auth) > 0:
                    print(f"      ✅ ENCONTRADO CHANGE_AUTH_FACTOR en AUTH_CHANGE_HISTORY!")
                    for _, reg in change_auth.head(5).iterrows():
                        print(f"         {reg.get('MODIFIED_ON', 'N/A')} - Auth_ID: {reg.get('AUTHENTICATION_ID', 'N/A')}")
                else:
                    print(f"      ❌ NO hay CHANGE_AUTH_FACTOR en AUTH_CHANGE_HISTORY")
            
        except Exception as e:
            print(f"      ❌ ERROR: {e}")
    else:
        print(f"   ❌ USER_AUTH_CHANGE_HISTORY.parquet no encontrado")

def investigar_logs_sistema():
    """Investigar logs del sistema para pistas"""
    print("   📋 INVESTIGACIÓN DE LOGS DEL SISTEMA")
    
    # Buscar logs del pipeline
    log_files = [
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios.log",
        "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/execution_status.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"   📄 EVIDENCIA: {os.path.basename(log_file)}")
            try:
                with open(log_file, 'r') as f:
                    contenido = f.read()
                
                # Buscar menciones de USER_MODIFICATION
                lineas = contenido.split('\n')
                lineas_relevantes = []
                
                for linea in lineas:
                    if any(palabra in linea.upper() for palabra in ['USER_MODIFICATION', 'CHANGE_AUTH', 'ERROR', 'WARNING']):
                        lineas_relevantes.append(linea)
                
                if lineas_relevantes:
                    print(f"      🔍 LÍNEAS RELEVANTES:")
                    for linea in lineas_relevantes[-10:]:  # Últimas 10
                        print(f"         {linea}")
                else:
                    print(f"      ✅ No se encontraron líneas relevantes")
                    
            except Exception as e:
                print(f"      ❌ ERROR: {e}")
        else:
            print(f"   ❌ Log no encontrado: {log_file}")

def generar_reporte_detective():
    """Generar reporte final del detective"""
    print(f"\n🕵️‍♂️ REPORTE FINAL DEL DETECTIVE EXTREMO")
    print("=" * 60)
    
    print(f"📋 EVIDENCIAS RECOPILADAS:")
    print(f"   1. ✅ Usuario existe en USER_DATA_TRX (22:50:03)")
    print(f"   2. ❌ NO hay CHANGE_AUTH_FACTOR en USER_MODIFICATION_DAY")
    print(f"   3. ✅ Registro CPIN existe en archivo original Oracle (22:52:32)")
    print(f"   4. ⏰ Diferencia temporal: 2.5 minutos")
    
    print(f"\n🎯 HIPÓTESIS PRINCIPALES:")
    print(f"   A. CORTE TEMPORAL: Extracción S3 termina antes de 22:52:32")
    print(f"   B. FILTRO OCULTO: Hay filtro que excluye CHANGE_AUTH_FACTOR")
    print(f"   C. RETRASO REPLICACIÓN: Oracle → S3 tiene retraso")
    print(f"   D. TABLA INCORRECTA: CHANGE_AUTH_FACTOR está en otra tabla")
    
    print(f"\n🔍 PRÓXIMAS ACCIONES INVESTIGATIVAS:")
    print(f"   1. Verificar configuración temporal de extracción S3")
    print(f"   2. Revisar tabla USER_AUTH_CHANGE_HISTORY")
    print(f"   3. Consultar directamente Oracle para confirmar datos")
    print(f"   4. Verificar logs de sincronización Oracle → S3")

if __name__ == "__main__":
    print("🚨 INICIANDO INVESTIGACIÓN DETECTIVE EXTREMO 🚨")
    print("=" * 70)
    
    detective_extremo_investigation()
    generar_reporte_detective()
    
    print(f"\n✅ INVESTIGACIÓN DETECTIVE COMPLETADA")
    print(f"🎯 CASO: PARCIALMENTE RESUELTO - Se requiere acceso a Oracle para confirmación final")
