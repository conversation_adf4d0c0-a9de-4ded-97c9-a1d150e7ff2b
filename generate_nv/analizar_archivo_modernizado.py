#!/usr/bin/env python3
"""
Script para analizar el archivo CSV del pipeline modernizado
"""
import pandas as pd
import sys
from collections import Counter

def analizar_archivo_csv(archivo_path):
    """Analiza el archivo CSV y muestra estadísticas"""
    print(f"=== ANÁLISIS DEL ARCHIVO: {archivo_path} ===\n")
    
    try:
        # Leer el archivo CSV
        df = pd.read_csv(archivo_path, header=None)
        
        # Información básica
        print(f"📊 ESTADÍSTICAS BÁSICAS:")
        print(f"   • Total de registros: {len(df):,}")
        print(f"   • Total de columnas: {len(df.columns)}")
        print(f"   • Tamaño del archivo: {archivo_path}")
        
        # Asignar nombres de columnas basados en la estructura conocida
        columnas_base = [
            'OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO',
            'DOCUMENTO', 'CELULAR', 'EMPRESA', 'COL9', 'COL10', 'COL11', 'COL12',
            'COL13', 'COL14', 'COL15', 'COL16', 'COL17', 'DESCRIPCION', 'ACCOUNTID',
            'COL20', 'COL21', 'COL22', 'USERID', 'COL24', 'COL25', 'COL26', 'COL27', 'COL28'
        ]

        # Generar nombres de columnas dinámicamente según el número real de columnas
        num_cols = len(df.columns)
        if num_cols <= len(columnas_base):
            columnas = columnas_base[:num_cols]
        else:
            columnas = columnas_base + [f'COL{i}' for i in range(len(columnas_base)+1, num_cols+1)]

        df.columns = columnas
        
        print(f"\n📋 ANÁLISIS POR OPERACIÓN:")
        operaciones = df['OPERACION'].value_counts()
        for op, count in operaciones.items():
            print(f"   • {op}: {count:,} registros")
        
        print(f"\n📋 ANÁLISIS POR TIPO DE DOCUMENTO:")
        tipos_doc = df['TIPODOCUMENTO'].value_counts()
        for tipo, count in tipos_doc.items():
            if pd.notna(tipo):
                print(f"   • {tipo}: {count:,} registros")
        
        print(f"\n📋 ANÁLISIS POR EMPRESA:")
        empresas = df['EMPRESA'].value_counts()
        for empresa, count in empresas.items():
            if pd.notna(empresa):
                print(f"   • {empresa}: {count:,} registros")
        
        # Verificar valores nulos en campos críticos
        print(f"\n🔍 VERIFICACIÓN DE VALORES NULOS:")
        campos_criticos = ['OPERACION', 'TRANSACTIONID', 'TIPODOCUMENTO', 'DOCUMENTO', 'CELULAR']
        for campo in campos_criticos:
            if campo in df.columns:
                nulos = df[campo].isnull().sum()
                print(f"   • {campo}: {nulos:,} valores nulos")
        
        # Mostrar primeros registros
        print(f"\n📄 PRIMEROS 5 REGISTROS:")
        print(df.head().to_string(index=False))
        
        # Verificar patrones de fecha
        print(f"\n📅 ANÁLISIS DE FECHAS:")
        if 'FECHA_HORA' in df.columns:
            fechas_unicas = df['FECHA_HORA'].str[:10].value_counts()
            print(f"   • Fechas encontradas:")
            for fecha, count in fechas_unicas.items():
                if pd.notna(fecha):
                    print(f"     - {fecha}: {count:,} registros")
        
        return df
        
    except Exception as e:
        print(f"❌ Error al analizar el archivo: {e}")
        return None

if __name__ == "__main__":
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609074148.csv"
    
    print("🔍 ANÁLISIS DETALLADO DEL ARCHIVO DEL PIPELINE MODERNIZADO")
    print("=" * 70)
    
    df = analizar_archivo_csv(archivo)
    
    if df is not None:
        print(f"\n✅ Análisis completado exitosamente")
        print(f"📊 Resumen: {len(df):,} registros procesados")
    else:
        print(f"\n❌ No se pudo completar el análisis")
