#!/usr/bin/env python3
"""
Script para verificar el flujo original de Oracle y confirmar si procesa transacciones nocturnas
"""
import pandas as pd
import os

def analizar_archivo_original():
    """Analiza el archivo original para verificar el rango horario"""
    print("🔍 VERIFICACIÓN DEL FLUJO ORIGINAL DE ORACLE")
    print("=" * 50)
    
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"📄 Analizando archivo original: {os.path.basename(archivo_original)}")
    
    if not os.path.exists(archivo_original):
        print(f"❌ Archivo original no encontrado: {archivo_original}")
        return None
    
    try:
        # Leer el archivo original
        df = pd.read_csv(archivo_original, header=None)
        
        print(f"📊 Estadísticas del archivo original:")
        print(f"   • Total de registros: {len(df):,}")
        print(f"   • Total de columnas: {len(df.columns)}")
        
        # Asignar nombres de columnas
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df.columns = columnas[:len(df.columns)]
        
        # Analizar rango horario
        print(f"\n🕐 ANÁLISIS DE RANGO HORARIO EN EL FLUJO ORIGINAL:")
        
        fechas_horas = df['FECHA_HORA'].dropna()
        
        if len(fechas_horas) > 0:
            # Extraer horas
            horas = []
            fechas_muestra = []
            
            for fecha_hora in fechas_horas:
                try:
                    if isinstance(fecha_hora, str) and len(fecha_hora) >= 19:
                        hora = fecha_hora[11:13]  # Extraer HH
                        if hora.isdigit():
                            horas.append(int(hora))
                            if len(fechas_muestra) < 20:  # Guardar muestra
                                fechas_muestra.append(fecha_hora)
                except:
                    pass
            
            if horas:
                hora_min = min(horas)
                hora_max = max(horas)
                
                print(f"   📊 Rango horario completo: {hora_min:02d}:XX - {hora_max:02d}:XX")
                
                # Contar registros por hora
                conteo_horas = {}
                for hora in horas:
                    conteo_horas[hora] = conteo_horas.get(hora, 0) + 1
                
                print(f"\n   📋 Distribución por hora:")
                for hora in sorted(conteo_horas.keys()):
                    print(f"      {hora:02d}:XX - {conteo_horas[hora]:,} registros")
                
                # Verificar si hay transacciones nocturnas (20:00 - 23:59)
                transacciones_nocturnas = sum(conteo_horas.get(h, 0) for h in range(20, 24))
                transacciones_totales = len(horas)
                
                print(f"\n   🌙 ANÁLISIS DE TRANSACCIONES NOCTURNAS (20:00-23:59):")
                print(f"      • Transacciones nocturnas: {transacciones_nocturnas:,}")
                print(f"      • Transacciones totales: {transacciones_totales:,}")
                print(f"      • Porcentaje nocturno: {(transacciones_nocturnas/transacciones_totales)*100:.2f}%")
                
                # Verificar específicamente la hora 22:XX
                registros_22h = conteo_horas.get(22, 0)
                print(f"      • Registros a las 22:XX: {registros_22h:,}")
                
                if registros_22h > 0:
                    print(f"   ✅ EL FLUJO ORIGINAL SÍ INCLUYE TRANSACCIONES A LAS 22:XX")
                    
                    # Buscar el registro específico que nos falta
                    buscar_registro_especifico(df)
                else:
                    print(f"   ❌ El flujo original NO tiene transacciones a las 22:XX")
                
                # Mostrar muestra de fechas
                print(f"\n   📄 Muestra de fechas/horas en el archivo original:")
                for i, fecha in enumerate(fechas_muestra[:10]):
                    print(f"      {i+1}. {fecha}")
                
                return conteo_horas
            else:
                print(f"   ❌ No se pudieron extraer horas del archivo")
                return None
        else:
            print(f"   ❌ No se encontraron fechas/horas en el archivo")
            return None
            
    except Exception as e:
        print(f"❌ Error analizando archivo original: {e}")
        return None

def buscar_registro_especifico(df_original):
    """Busca el registro específico que nos falta en el archivo original"""
    print(f"\n🎯 BÚSQUEDA DEL REGISTRO ESPECÍFICO EN EL ARCHIVO ORIGINAL:")
    
    documento_faltante = "71793435"
    celular_faltante = "51907368782"
    
    # Buscar el registro
    mask = (df_original['DOCUMENTO'].astype(str) == documento_faltante) & \
           (df_original['CELULAR'].astype(str) == celular_faltante)
    
    registros_encontrados = df_original[mask]
    
    if len(registros_encontrados) > 0:
        print(f"   ✅ REGISTRO ENCONTRADO EN EL ARCHIVO ORIGINAL!")
        print(f"   📊 Registros encontrados: {len(registros_encontrados)}")
        
        for i, (_, registro) in enumerate(registros_encontrados.iterrows(), 1):
            print(f"\n   📄 Registro #{i}:")
            print(f"      • Operación: {registro['OPERACION']}")
            print(f"      • TransactionID: {registro['TRANSACTIONID']}")
            print(f"      • Fecha/Hora: {registro['FECHA_HORA']}")
            print(f"      • Documento: {registro['TIPODOCUMENTO']} {registro['DOCUMENTO']}")
            print(f"      • Celular: {registro['CELULAR']}")
            print(f"      • Empresa: {registro['EMPRESA']}")
            
            # Verificar si es exactamente el registro que buscamos
            if "22:52" in str(registro['FECHA_HORA']):
                print(f"      🎯 ¡ESTE ES EL REGISTRO FALTANTE!")
    else:
        print(f"   ❌ Registro NO encontrado en el archivo original")
        print(f"   💡 Esto sugiere que el registro no debería estar en el pipeline modernizado")

def comparar_rangos_horarios():
    """Compara los rangos horarios entre original y modernizado"""
    print(f"\n🔄 COMPARACIÓN DE RANGOS HORARIOS:")
    
    # Analizar archivo modernizado
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609074148.csv"
    
    if os.path.exists(archivo_modernizado):
        try:
            df_mod = pd.read_csv(archivo_modernizado, header=None)
            
            if len(df_mod.columns) >= 3:
                fechas_mod = df_mod.iloc[:, 2].dropna()
                
                horas_mod = []
                for fecha in fechas_mod:
                    try:
                        if isinstance(fecha, str) and len(fecha) >= 19:
                            hora = fecha[11:13]
                            if hora.isdigit():
                                horas_mod.append(int(hora))
                    except:
                        pass
                
                if horas_mod:
                    hora_min_mod = min(horas_mod)
                    hora_max_mod = max(horas_mod)
                    
                    print(f"   📊 Modernizado: {hora_min_mod:02d}:XX - {hora_max_mod:02d}:XX")
                    
                    # Contar por hora
                    conteo_mod = {}
                    for hora in horas_mod:
                        conteo_mod[hora] = conteo_mod.get(hora, 0) + 1
                    
                    registros_22h_mod = conteo_mod.get(22, 0)
                    print(f"   📊 Registros 22:XX en modernizado: {registros_22h_mod:,}")
                    
                    return conteo_mod
        except Exception as e:
            print(f"   ❌ Error analizando modernizado: {e}")
    
    return None

def conclusion_investigacion(conteo_original, conteo_modernizado):
    """Genera conclusiones de la investigación"""
    print(f"\n📋 CONCLUSIONES DE LA INVESTIGACIÓN:")
    
    if conteo_original:
        registros_22h_orig = conteo_original.get(22, 0)
        
        if registros_22h_orig > 0:
            print(f"   ✅ El flujo original SÍ procesa transacciones nocturnas (22:XX)")
            print(f"   📊 Registros 22:XX en original: {registros_22h_orig:,}")
            
            if conteo_modernizado:
                registros_22h_mod = conteo_modernizado.get(22, 0)
                print(f"   📊 Registros 22:XX en modernizado: {registros_22h_mod:,}")
                
                if registros_22h_mod == 0:
                    print(f"   ❌ PROBLEMA CONFIRMADO: El pipeline modernizado NO procesa transacciones nocturnas")
                    print(f"   🔧 ACCIÓN REQUERIDA: Corregir filtros en el pipeline para incluir 24 horas")
                else:
                    print(f"   ⚠️  Diferencia en registros nocturnas - investigar más")
            
            return True
        else:
            print(f"   ✅ El flujo original NO procesa transacciones nocturnas")
            print(f"   💡 El pipeline modernizado está correcto al excluir horario nocturno")
            return False
    else:
        print(f"   ❌ No se pudo determinar el comportamiento del flujo original")
        return None

if __name__ == "__main__":
    print("🕵️ VERIFICACIÓN DEL FLUJO ORIGINAL DE ORACLE")
    print("=" * 60)
    
    conteo_original = analizar_archivo_original()
    conteo_modernizado = comparar_rangos_horarios()
    
    resultado = conclusion_investigacion(conteo_original, conteo_modernizado)
    
    print(f"\n✅ Verificación completada")
    
    if resultado is True:
        print(f"\n🔧 RECOMENDACIÓN: Corregir el pipeline modernizado para incluir transacciones nocturnas")
    elif resultado is False:
        print(f"\n✅ CONFIRMACIÓN: El pipeline modernizado está funcionando correctamente")
    else:
        print(f"\n⚠️  INVESTIGACIÓN ADICIONAL REQUERIDA")
