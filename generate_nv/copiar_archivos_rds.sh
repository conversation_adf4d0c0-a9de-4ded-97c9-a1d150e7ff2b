#!/bin/bash

# Script para copiar archivos de transacciones a la carpeta de carga RDS
# Uso: sh copiar_archivos_rds.sh "YYYY/MM/DD"

# Definir variables
if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

echo "Procesando archivos para la fecha: $fecha"

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")
ROUTE_CSV="/home/<USER>/output/csv"
TARGET_PATH="/home/<USER>/output/load_rds"

# Crear directorio de destino
mkdir -p "$TARGET_PATH"

# Definir nombres de archivos
log_trx_file="TR-${fecha_path}.csv"
mtx_trx_header_file="MTX_TRANSACTION_HEADER_${fecha_path}.csv"
log_trx_new_name="LOG_TRX_FINAL.csv"
mtx_trx_new_name="MTX_TRANSACTION_HEADER.csv"

echo "Buscando archivos:"
echo "- $ROUTE_CSV/$log_trx_file"
echo "- $ROUTE_CSV/$mtx_trx_header_file"

# Copiar archivo de log de transacciones
if [ -f "$ROUTE_CSV/$log_trx_file" ]; then
    cp "$ROUTE_CSV/$log_trx_file" "$TARGET_PATH/$log_trx_new_name"
    echo "✅ Archivo $log_trx_file copiado a $TARGET_PATH/$log_trx_new_name"
else
    echo "❌ No se encontró el archivo $log_trx_file"
fi

# Copiar archivo de transacciones MTX
if [ -f "$ROUTE_CSV/$mtx_trx_header_file" ]; then
    cp "$ROUTE_CSV/$mtx_trx_header_file" "$TARGET_PATH/$mtx_trx_new_name"
    echo "✅ Archivo $mtx_trx_header_file copiado a $TARGET_PATH/$mtx_trx_new_name"
else
    echo "❌ No se encontró el archivo $mtx_trx_header_file"
fi

echo "Proceso completado."
echo "Para cargar estos archivos en RDS, ejecute:"
echo "cd /home/<USER>/generate/prepare_rds/ && python3 read_csv_sql.py \"$TARGET_PATH\""
