#!/usr/bin/env python3
"""
🥷 DEBUG USER MODIFICATION
Investigar por qué User Modification no se convierte a CCEL
"""
import pandas as pd
import duckdb

def debug_user_modification():
    """Debug específico de User Modification"""
    print("🥷 DEBUG USER MODIFICATION")
    print("=" * 40)
    
    # Archivo con lógica ninja
    ninja_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_NINJA_FINAL.parquet"
    documento_test = "03897204"
    
    print(f"🎯 Documento de prueba: {documento_test}")
    print(f"📄 Archivo ninja: {ninja_file}")
    
    try:
        # Leer archivo ninja
        df = pd.read_parquet(ninja_file)
        
        # Buscar documento específico
        reg = df[df['DOCUMENTO'] == documento_test]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            print(f"\n📊 REGISTRO EN ARCHIVO NINJA:")
            print(f"   REQUESTTYPE: {registro['REQUESTTYPE']}")
            print(f"   CREATEDON: {registro['CREATEDON']}")
            print(f"   OLDDATA: {registro.get('OLDDATA', 'N/A')}")
            print(f"   NEWDATA: {registro.get('NEWDATA', 'N/A')}")
            
            # Verificar si contiene mobileNumber
            old_data = str(registro.get('OLDDATA', ''))
            new_data = str(registro.get('NEWDATA', ''))
            
            print(f"\n🔍 ANÁLISIS DE DATOS:")
            print(f"   Contiene 'mobileNumber' en OLDDATA: {'mobileNumber' in old_data}")
            print(f"   Contiene 'mobileNumber' en NEWDATA: {'mobileNumber' in new_data}")
            
            if 'mobileNumber' in old_data:
                print(f"   📋 OLDDATA snippet: ...{old_data[old_data.find('mobileNumber')-20:old_data.find('mobileNumber')+50]}...")
            
            if 'mobileNumber' in new_data:
                print(f"   📋 NEWDATA snippet: ...{new_data[new_data.find('mobileNumber')-20:new_data.find('mobileNumber')+50]}...")
        else:
            print(f"❌ Documento {documento_test} no encontrado en archivo ninja")
        
        # Verificar todos los User Modification
        user_mod_regs = df[df['REQUESTTYPE'] == 'User Modification']
        print(f"\n📊 TOTAL USER MODIFICATION EN ARCHIVO: {len(user_mod_regs)}")
        
        if len(user_mod_regs) > 0:
            print(f"📋 Primeros 5 User Modification:")
            for i, (_, reg) in enumerate(user_mod_regs.head().iterrows(), 1):
                old_data = str(reg.get('OLDDATA', ''))
                new_data = str(reg.get('NEWDATA', ''))
                has_mobile = 'mobileNumber' in old_data or 'mobileNumber' in new_data
                print(f"   {i}. Doc {reg['DOCUMENTO']} - Mobile: {'✅' if has_mobile else '❌'}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_procesamiento_manual():
    """Test manual del procesamiento"""
    print(f"\n🧪 TEST PROCESAMIENTO MANUAL:")
    
    # Simular datos de User Modification
    test_data = {
        'REQUESTTYPE': 'User Modification',
        'OLDDATA': '{"mobileNumber":"51902887798","firstName":"JUAN"}',
        'NEWDATA': '{"mobileNumber":"51902887799","firstName":"JUAN"}'
    }
    
    print(f"📊 Datos de prueba:")
    print(f"   REQUESTTYPE: {test_data['REQUESTTYPE']}")
    print(f"   OLDDATA: {test_data['OLDDATA']}")
    print(f"   NEWDATA: {test_data['NEWDATA']}")
    
    # Aplicar lógica ninja
    request_type = test_data['REQUESTTYPE']
    old_data_str = str(test_data['OLDDATA'])
    new_data_str = str(test_data['NEWDATA'])
    
    if request_type == 'User Modification':
        if 'mobileNumber' in old_data_str or 'mobileNumber' in new_data_str:
            campo_modificado = 'CCEL'
        else:
            campo_modificado = 'User Modification'
    else:
        campo_modificado = request_type
    
    print(f"\n🔧 RESULTADO LÓGICA NINJA:")
    print(f"   Campo modificado: {campo_modificado}")
    print(f"   ✅ Debería ser CCEL: {'✅' if campo_modificado == 'CCEL' else '❌'}")

if __name__ == "__main__":
    debug_user_modification()
    test_procesamiento_manual()
