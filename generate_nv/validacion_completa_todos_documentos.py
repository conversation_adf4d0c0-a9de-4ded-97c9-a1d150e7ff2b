#!/usr/bin/env python3
"""
🔍 VALIDACIÓN COMPLETA - TODOS LOS DOCUMENTOS
Verifica que TODOS los documentos cuadren perfectamente con Oracle
"""
import pandas as pd
import random

def validacion_completa_todos_documentos():
    """Validación completa de homologación de todos los documentos"""
    print("🔍 VALIDACIÓN COMPLETA - TODOS LOS DOCUMENTOS")
    print("=" * 60)
    
    # Archivos finales con lógica Oracle corregida
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609212231.csv"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"📄 Modernizado (CORREGIDO): {archivo_modernizado.split('/')[-1]}")
    print(f"📄 Oracle: {archivo_oracle.split('/')[-1]}")
    
    # 1. Análisis general de archivos
    print(f"\n1️⃣ ANÁLISIS GENERAL DE ARCHIVOS:")
    stats_general = analizar_archivos_general(archivo_modernizado, archivo_oracle)
    
    # 2. Análisis de documentos comunes
    print(f"\n2️⃣ ANÁLISIS DE DOCUMENTOS COMUNES:")
    documentos_comunes = analizar_documentos_comunes(archivo_modernizado, archivo_oracle)
    
    # 3. Validación de muestra aleatoria
    print(f"\n3️⃣ VALIDACIÓN MUESTRA ALEATORIA (50 documentos):")
    resultado_muestra = validar_muestra_aleatoria(archivo_modernizado, archivo_oracle, documentos_comunes, 50)
    
    # 4. Validación de documentos críticos conocidos
    print(f"\n4️⃣ VALIDACIÓN DOCUMENTOS CRÍTICOS:")
    documentos_criticos = ["70635331", "76730654", "60918019"]
    resultado_criticos = validar_documentos_criticos(archivo_modernizado, archivo_oracle, documentos_criticos)
    
    # 5. Análisis de diferencias
    print(f"\n5️⃣ ANÁLISIS DE DIFERENCIAS:")
    analizar_diferencias(archivo_modernizado, archivo_oracle, documentos_comunes)
    
    # 6. Resumen final
    print(f"\n6️⃣ RESUMEN FINAL:")
    generar_resumen_final(stats_general, resultado_muestra, resultado_criticos)

def analizar_archivos_general(archivo_modernizado, archivo_oracle):
    """Análisis general de los archivos"""
    try:
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Estadísticas generales
        stats = {
            'registros_modernizado': len(df_mod),
            'registros_oracle': len(df_oracle),
            'documentos_modernizado': df_mod['DOCUMENTO'].nunique(),
            'documentos_oracle': df_oracle['DOCUMENTO'].nunique()
        }
        
        print(f"   📊 Registros:")
        print(f"      Modernizado: {stats['registros_modernizado']:,}")
        print(f"      Oracle: {stats['registros_oracle']:,}")
        print(f"      Diferencia: {abs(stats['registros_modernizado'] - stats['registros_oracle']):,}")
        
        print(f"   📊 Documentos únicos:")
        print(f"      Modernizado: {stats['documentos_modernizado']:,}")
        print(f"      Oracle: {stats['documentos_oracle']:,}")
        print(f"      Diferencia: {abs(stats['documentos_modernizado'] - stats['documentos_oracle']):,}")
        
        return stats
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {}

def analizar_documentos_comunes(archivo_modernizado, archivo_oracle):
    """Analiza documentos comunes entre ambos archivos"""
    try:
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Documentos únicos
        docs_mod = set(df_mod['DOCUMENTO'].astype(str))
        docs_oracle = set(df_oracle['DOCUMENTO'].astype(str))
        
        # Análisis de intersección
        docs_comunes = docs_mod.intersection(docs_oracle)
        docs_solo_mod = docs_mod - docs_oracle
        docs_solo_oracle = docs_oracle - docs_mod
        
        print(f"   📊 Documentos comunes: {len(docs_comunes):,}")
        print(f"   📊 Solo en modernizado: {len(docs_solo_mod):,}")
        print(f"   📊 Solo en Oracle: {len(docs_solo_oracle):,}")
        
        if len(docs_solo_mod) > 0:
            print(f"   📋 Ejemplos solo en modernizado: {list(docs_solo_mod)[:5]}")
        
        if len(docs_solo_oracle) > 0:
            print(f"   📋 Ejemplos solo en Oracle: {list(docs_solo_oracle)[:5]}")
        
        return list(docs_comunes)
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def validar_muestra_aleatoria(archivo_modernizado, archivo_oracle, documentos_comunes, tamaño_muestra):
    """Valida una muestra aleatoria de documentos"""
    try:
        if len(documentos_comunes) == 0:
            print(f"   ❌ No hay documentos comunes para validar")
            return {'homologacion_perfecta': 0, 'total': 0}
        
        # Seleccionar muestra aleatoria
        muestra = random.sample(documentos_comunes, min(tamaño_muestra, len(documentos_comunes)))
        
        print(f"   📊 Validando muestra de {len(muestra)} documentos...")
        
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        # Validar cada documento de la muestra
        homologacion_perfecta = 0
        homologacion_parcial = 0
        sin_homologacion = 0
        
        for i, documento in enumerate(muestra, 1):
            if i <= 10:  # Mostrar detalles solo de los primeros 10
                resultado = validar_documento_individual(df_mod, df_oracle, documento, mostrar_detalles=True)
            else:
                resultado = validar_documento_individual(df_mod, df_oracle, documento, mostrar_detalles=False)
            
            if resultado == 'perfecta':
                homologacion_perfecta += 1
            elif resultado == 'parcial':
                homologacion_parcial += 1
            else:
                sin_homologacion += 1
        
        # Resultados de la muestra
        total = len(muestra)
        porcentaje_perfecta = (homologacion_perfecta / total * 100) if total > 0 else 0
        porcentaje_parcial = (homologacion_parcial / total * 100) if total > 0 else 0
        porcentaje_sin = (sin_homologacion / total * 100) if total > 0 else 0
        
        print(f"\n   📊 RESULTADOS DE MUESTRA:")
        print(f"      🏆 Homologación perfecta: {homologacion_perfecta}/{total} ({porcentaje_perfecta:.1f}%)")
        print(f"      ⚠️  Homologación parcial: {homologacion_parcial}/{total} ({porcentaje_parcial:.1f}%)")
        print(f"      ❌ Sin homologación: {sin_homologacion}/{total} ({porcentaje_sin:.1f}%)")
        
        return {
            'homologacion_perfecta': homologacion_perfecta,
            'homologacion_parcial': homologacion_parcial,
            'sin_homologacion': sin_homologacion,
            'total': total,
            'porcentaje_perfecta': porcentaje_perfecta
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {'homologacion_perfecta': 0, 'total': 0}

def validar_documento_individual(df_mod, df_oracle, documento, mostrar_detalles=False):
    """Valida un documento individual"""
    try:
        # Buscar en ambos archivos
        reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
        reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
        
        if len(reg_mod) == 0 or len(reg_oracle) == 0:
            if mostrar_detalles:
                print(f"      ❌ Doc {documento}: No encontrado en uno de los archivos")
            return 'sin_homologacion'
        
        # Comparar campos clave (excluyendo TransactionID)
        mod_reg = reg_mod.iloc[0]
        oracle_reg = reg_oracle.iloc[0]
        
        campos_coinciden = (
            mod_reg['OPERACION'] == oracle_reg['OPERACION'] and
            mod_reg['FECHA_HORA'] == oracle_reg['FECHA_HORA'] and
            mod_reg['DOCUMENTO'] == oracle_reg['DOCUMENTO'] and
            mod_reg['CELULAR'] == oracle_reg['CELULAR'] and
            mod_reg['EMPRESA'] == oracle_reg['EMPRESA']
        )
        
        if campos_coinciden:
            if mostrar_detalles:
                print(f"      ✅ Doc {documento}: HOMOLOGACIÓN PERFECTA ({mod_reg['FECHA_HORA']})")
            return 'perfecta'
        else:
            if mostrar_detalles:
                print(f"      ⚠️  Doc {documento}: HOMOLOGACIÓN PARCIAL")
                print(f"         Mod: {mod_reg['OPERACION']} - {mod_reg['FECHA_HORA']}")
                print(f"         Ora: {oracle_reg['OPERACION']} - {oracle_reg['FECHA_HORA']}")
            return 'parcial'
            
    except Exception as e:
        if mostrar_detalles:
            print(f"      ❌ Doc {documento}: Error - {e}")
        return 'sin_homologacion'

def validar_documentos_criticos(archivo_modernizado, archivo_oracle, documentos_criticos):
    """Valida documentos críticos específicos"""
    try:
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        criticos_ok = 0
        
        for documento in documentos_criticos:
            resultado = validar_documento_individual(df_mod, df_oracle, documento, mostrar_detalles=True)
            if resultado == 'perfecta':
                criticos_ok += 1
        
        print(f"\n   📊 DOCUMENTOS CRÍTICOS: {criticos_ok}/{len(documentos_criticos)} homologados perfectamente")
        
        return {
            'criticos_ok': criticos_ok,
            'total_criticos': len(documentos_criticos)
        }
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return {'criticos_ok': 0, 'total_criticos': 0}

def analizar_diferencias(archivo_modernizado, archivo_oracle, documentos_comunes):
    """Analiza las diferencias principales entre archivos"""
    try:
        if len(documentos_comunes) == 0:
            print(f"   ❌ No hay documentos comunes para analizar diferencias")
            return
        
        # Tomar muestra para análisis de diferencias
        muestra_diferencias = random.sample(documentos_comunes, min(20, len(documentos_comunes)))
        
        # Leer archivos
        df_mod = pd.read_csv(archivo_modernizado, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        diferencias_timestamp = 0
        diferencias_operacion = 0
        diferencias_celular = 0
        diferencias_empresa = 0
        
        for documento in muestra_diferencias:
            reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
            reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
            
            if len(reg_mod) > 0 and len(reg_oracle) > 0:
                mod_reg = reg_mod.iloc[0]
                oracle_reg = reg_oracle.iloc[0]
                
                if mod_reg['FECHA_HORA'] != oracle_reg['FECHA_HORA']:
                    diferencias_timestamp += 1
                if mod_reg['OPERACION'] != oracle_reg['OPERACION']:
                    diferencias_operacion += 1
                if mod_reg['CELULAR'] != oracle_reg['CELULAR']:
                    diferencias_celular += 1
                if mod_reg['EMPRESA'] != oracle_reg['EMPRESA']:
                    diferencias_empresa += 1
        
        total_muestra = len(muestra_diferencias)
        
        print(f"   📊 ANÁLISIS DE DIFERENCIAS (muestra de {total_muestra}):")
        print(f"      Diferencias en TIMESTAMP: {diferencias_timestamp}/{total_muestra} ({diferencias_timestamp/total_muestra*100:.1f}%)")
        print(f"      Diferencias en OPERACIÓN: {diferencias_operacion}/{total_muestra} ({diferencias_operacion/total_muestra*100:.1f}%)")
        print(f"      Diferencias en CELULAR: {diferencias_celular}/{total_muestra} ({diferencias_celular/total_muestra*100:.1f}%)")
        print(f"      Diferencias en EMPRESA: {diferencias_empresa}/{total_muestra} ({diferencias_empresa/total_muestra*100:.1f}%)")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")

def generar_resumen_final(stats_general, resultado_muestra, resultado_criticos):
    """Genera el resumen final de la validación"""
    
    print(f"   🏆 RESUMEN FINAL DE VALIDACIÓN:")
    
    # Estadísticas generales
    if stats_general:
        diferencia_registros = abs(stats_general['registros_modernizado'] - stats_general['registros_oracle'])
        diferencia_documentos = abs(stats_general['documentos_modernizado'] - stats_general['documentos_oracle'])
        
        print(f"      📊 Diferencia en registros: {diferencia_registros:,}")
        print(f"      📊 Diferencia en documentos: {diferencia_documentos:,}")
    
    # Resultados de muestra
    if resultado_muestra.get('total', 0) > 0:
        porcentaje = resultado_muestra['porcentaje_perfecta']
        print(f"      📊 Homologación perfecta (muestra): {porcentaje:.1f}%")
        
        if porcentaje >= 95:
            print(f"      ✅ EXCELENTE: Homologación casi perfecta")
        elif porcentaje >= 85:
            print(f"      ✅ BUENO: Homologación muy buena")
        elif porcentaje >= 70:
            print(f"      ⚠️  REGULAR: Homologación aceptable")
        else:
            print(f"      ❌ MALO: Homologación requiere corrección")
    
    # Documentos críticos
    if resultado_criticos.get('total_criticos', 0) > 0:
        criticos_porcentaje = (resultado_criticos['criticos_ok'] / resultado_criticos['total_criticos'] * 100)
        print(f"      📊 Documentos críticos: {criticos_porcentaje:.1f}% homologados")
        
        if criticos_porcentaje == 100:
            print(f"      ✅ PERFECTO: Todos los documentos críticos homologan")
        else:
            print(f"      ⚠️  ATENCIÓN: Algunos documentos críticos no homologan")
    
    # Conclusión final
    print(f"\n   🎯 CONCLUSIÓN FINAL:")
    
    if (resultado_muestra.get('porcentaje_perfecta', 0) >= 95 and 
        resultado_criticos.get('criticos_ok', 0) == resultado_criticos.get('total_criticos', 1)):
        print(f"      🏆 HOMOLOGACIÓN EXITOSA - 'COMO DOS GOTAS DE AGUA'")
        print(f"      ✅ La lógica Oracle corregida funciona perfectamente")
    elif resultado_muestra.get('porcentaje_perfecta', 0) >= 85:
        print(f"      ✅ HOMOLOGACIÓN MUY BUENA - Requiere ajustes menores")
    else:
        print(f"      ⚠️  HOMOLOGACIÓN REQUIERE MEJORAS")
        print(f"      🔧 Revisar lógica Oracle o filtros adicionales")

if __name__ == "__main__":
    print("🔍 VALIDACIÓN COMPLETA - TODOS LOS DOCUMENTOS")
    print("=" * 80)
    
    # Establecer semilla para reproducibilidad
    random.seed(42)
    
    validacion_completa_todos_documentos()
    
    print(f"\n✅ Validación completa finalizada")
