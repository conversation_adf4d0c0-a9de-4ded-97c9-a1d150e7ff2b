#!/usr/bin/env python3
"""
🥷 IMPLEMENTACIÓN SOLUCIÓN NINJA FINAL
Solución: Priorizar User Modification sobre CHANGE_AUTH_FACTOR para lograr 100% homologación
"""
import duckdb
import os

def implementar_solucion_ninja_final():
    """Implementa la solución ninja final para 100% homologación"""
    print("🥷 IMPLEMENTANDO SOLUCIÓN NINJA FINAL")
    print("=" * 50)
    
    # Archivos
    log_usr_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    output_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_NINJA_FINAL.parquet"
    
    print(f"📄 Input: {log_usr_path}")
    print(f"📄 Output: {output_path}")
    
    print(f"\n🔧 LÓGICA NINJA FINAL:")
    print(f"   1. PRIORIDAD 1: User Modification (Oracle prefiere esto → CCEL)")
    print(f"   2. PRIORIDAD 2: CHANGE_CELLPHONE")
    print(f"   3. PRIORIDAD 3: CHANGE_AUTH_FACTOR")
    print(f"   4. PRIORIDAD 4: Otros REQUESTTYPE")
    print(f"   5. CRITERIO SECUNDARIO: Horarios (MAÑANA > MADRUGADA > TARDE > NOCHE)")
    print(f"   6. CRITERIO TERCIARIO: MÁS ANTIGUO (CREATEDON ASC)")
    
    try:
        conn = duckdb.connect()
        
        # Query con lógica ninja final
        ninja_final_query = f"""
        CREATE OR REPLACE TABLE log_usr_ninja_final AS
        WITH registros_con_prioridad AS (
            SELECT *,
                -- Prioridad por REQUESTTYPE (Oracle prefiere User Modification)
                CASE 
                    WHEN REQUESTTYPE = 'User Modification' THEN 1
                    WHEN REQUESTTYPE = 'CHANGE_CELLPHONE' THEN 2
                    WHEN REQUESTTYPE = 'CHANGE_AUTH_FACTOR' THEN 3
                    ELSE 4
                END as requesttype_prioridad,
                -- Prioridad por horario
                CASE 
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 1  -- MAÑANA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 2   -- MADRUGADA
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 3 -- TARDE
                    ELSE 4  -- NOCHE (18-23)
                END as periodo_prioridad,
                CASE 
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 6 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 12 THEN 'MAÑANA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 0 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 6 THEN 'MADRUGADA'
                    WHEN date_part('hour', CAST(CREATEDON AS TIMESTAMP)) >= 12 AND date_part('hour', CAST(CREATEDON AS TIMESTAMP)) < 18 THEN 'TARDE'
                    ELSE 'NOCHE'
                END as periodo_nombre,
                ROW_NUMBER() OVER (
                    PARTITION BY USERHISTID, REQUESTTYPE
                    ORDER BY 
                        requesttype_prioridad ASC,  -- User Modification primero
                        periodo_prioridad ASC,      -- Luego por horario
                        CREATEDON ASC               -- Luego más antiguo
                ) as rn_final
            FROM read_parquet('{log_usr_path}')
        )
        SELECT 
            USERHISTID, CREATEDON, REQUESTTYPE, MSISDN, BANKDOMAIN, DOCUMENTO,
            TIPODOCUMENTO, NOMBRE, APELLIDO, NNOMBRE, NAPELLIDO, PERFILA, PERFILB,
            IDIOMAA, IDIOMAB, TELCOA, TELCOB, RAZON, PERFILCUENTA, PERFILCUENTAA,
            PERFILCUENTAB, TIPODOCUMENTOA, TIPODOCUMENTOB, DOCUMENTOB, NUMDOCUMENTOB,
            CREATED_BY, USERID, ACCOUNTTYPE, ACCOUNTID, MSISDNB,
            requesttype_prioridad, periodo_prioridad, periodo_nombre
        FROM registros_con_prioridad
        WHERE rn_final = 1  -- Solo el registro con mayor prioridad
        ORDER BY CREATEDON
        """
        
        print(f"\n⚙️  Ejecutando query ninja final...")
        conn.execute(ninja_final_query)
        
        # Verificar resultados
        original_count = conn.execute(f"SELECT COUNT(*) FROM read_parquet('{log_usr_path}')").fetchone()[0]
        ninja_count = conn.execute("SELECT COUNT(*) FROM log_usr_ninja_final").fetchone()[0]
        
        print(f"📊 Registros originales: {original_count:,}")
        print(f"📊 Registros con lógica ninja final: {ninja_count:,}")
        print(f"📊 Reducción: {original_count - ninja_count:,} registros")
        
        # Verificar documentos específicos CPIN vs CCEL
        print(f"\n🔍 VERIFICACIÓN DOCUMENTOS CPIN vs CCEL:")
        verificar_documentos_cpin_ccel(conn)
        
        # Verificar documentos críticos originales
        print(f"\n🔍 VERIFICACIÓN DOCUMENTOS CRÍTICOS:")
        verificar_documentos_criticos_ninja(conn)
        
        # Exportar resultado
        export_query = f"""
        COPY log_usr_ninja_final TO '{output_path}' (FORMAT PARQUET)
        """
        conn.execute(export_query)
        
        print(f"\n✅ Archivo generado: {output_path}")
        
        conn.close()
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def verificar_documentos_cpin_ccel(conn):
    """Verifica documentos que tenían diferencias CPIN vs CCEL"""
    
    # Documentos de muestra que tenían diferencias
    documentos_muestra = ["03897204", "03877508", "18206061", "41136171", "45723579"]
    
    for documento in documentos_muestra:
        query = f"""
        SELECT CREATEDON, REQUESTTYPE, requesttype_prioridad, periodo_nombre
        FROM log_usr_ninja_final
        WHERE DOCUMENTO = '{documento}'
        """
        
        result = conn.execute(query).fetchall()
        
        if len(result) > 0:
            timestamp, requesttype, req_prioridad, periodo = result[0]
            print(f"   📊 Doc {documento}:")
            print(f"      Ninja selecciona: {requesttype} - {timestamp} ({periodo})")
            print(f"      Prioridad REQUESTTYPE: {req_prioridad}")
            
            # Verificar si ahora selecciona User Modification (debería ser CCEL)
            if requesttype == 'User Modification':
                print(f"      ✅ CORRECTO: Selecciona User Modification → debería ser CCEL")
            elif requesttype == 'CHANGE_AUTH_FACTOR':
                print(f"      ⚠️  AÚN SELECCIONA: CHANGE_AUTH_FACTOR → será CPIN")
            else:
                print(f"      🔍 SELECCIONA: {requesttype}")
        else:
            print(f"   ❌ Doc {documento}: No encontrado")

def verificar_documentos_criticos_ninja(conn):
    """Verifica que los documentos críticos originales sigan funcionando"""
    
    documentos_criticos = {
        "70635331": "2025-06-09 00:28:11",
        "76730654": "2025-06-09 09:41:55", 
        "60918019": "2025-06-09 07:31:58"
    }
    
    for documento, timestamp_esperado in documentos_criticos.items():
        query = f"""
        SELECT CREATEDON, REQUESTTYPE, requesttype_prioridad, periodo_nombre
        FROM log_usr_ninja_final
        WHERE DOCUMENTO = '{documento}'
        """
        
        result = conn.execute(query).fetchall()
        
        if len(result) > 0:
            timestamp_obtenido, requesttype, req_prioridad, periodo = result[0]
            timestamp_str = str(timestamp_obtenido)
            
            print(f"   📊 Doc {documento}:")
            print(f"      Esperado: {timestamp_esperado}")
            print(f"      Obtenido: {timestamp_str}")
            print(f"      REQUESTTYPE: {requesttype} (Prioridad {req_prioridad})")
            print(f"      Período: {periodo}")
            
            if timestamp_str == timestamp_esperado:
                print(f"      ✅ PERFECTO - Documento crítico mantiene homologación")
            else:
                print(f"      ❌ ERROR - Documento crítico perdió homologación")
        else:
            print(f"   ❌ Doc {documento}: No encontrado")

def generar_csv_ninja_final():
    """Genera CSV final con la lógica ninja final"""
    print(f"\n🔄 GENERANDO CSV CON LÓGICA NINJA FINAL...")
    
    # Usar el archivo con lógica ninja final
    ninja_file = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR_NINJA_FINAL.parquet"
    
    if not os.path.exists(ninja_file):
        print(f"❌ Archivo ninja final no encontrado: {ninja_file}")
        return
    
    try:
        from S3_LOG_USER.procesar_log_usuarios import ProcesadorLogUsuarios
        import logging
        
        # Configurar logger
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        procesador = ProcesadorLogUsuarios(logger)
        csv_final_dir = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado"
        
        # Procesar con lógica ninja final
        archivos_procesados = procesador.procesar_log_usuarios(
            ninja_file, "2025-06-09", csv_final_dir
        )
        
        print(f"✅ Archivos CSV generados con lógica ninja final:")
        for archivo in archivos_procesados:
            print(f"   📁 {os.path.basename(archivo)}")
        
        return archivos_procesados
        
    except Exception as e:
        print(f"❌ Error generando CSV: {e}")
        return []

def validar_homologacion_ninja_final():
    """Valida la homologación final con lógica ninja"""
    print(f"\n🔍 VALIDACIÓN HOMOLOGACIÓN NINJA FINAL:")
    
    # Buscar el archivo CSV más reciente
    csv_dir = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado"
    
    try:
        import glob
        import pandas as pd
        
        archivos_csv = glob.glob(f"{csv_dir}/LOGUSR-FCOMPARTAMOS-*.csv")
        if archivos_csv:
            archivo_mas_reciente = max(archivos_csv, key=os.path.getctime)
            print(f"   📄 Archivo más reciente: {os.path.basename(archivo_mas_reciente)}")
            
            # Verificar documentos CPIN vs CCEL
            archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
            documentos_muestra = ["03897204", "03877508", "18206061"]
            
            # Leer archivos
            df_mod = pd.read_csv(archivo_mas_reciente, header=None)
            df_oracle = pd.read_csv(archivo_oracle, header=None)
            
            columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                       'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
            
            df_mod.columns = columnas[:len(df_mod.columns)]
            df_oracle.columns = columnas[:len(df_oracle.columns)]
            
            homologados = 0
            
            for documento in documentos_muestra:
                reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
                reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
                
                if len(reg_mod) > 0 and len(reg_oracle) > 0:
                    mod_op = reg_mod.iloc[0]['OPERACION']
                    oracle_op = reg_oracle.iloc[0]['OPERACION']
                    mod_ts = reg_mod.iloc[0]['FECHA_HORA']
                    oracle_ts = reg_oracle.iloc[0]['FECHA_HORA']
                    
                    print(f"   📊 Doc {documento}:")
                    print(f"      Ninja: {mod_op} - {mod_ts}")
                    print(f"      Oracle: {oracle_op} - {oracle_ts}")
                    
                    if mod_op == oracle_op and mod_ts == oracle_ts:
                        print(f"      ✅ HOMOLOGACIÓN PERFECTA")
                        homologados += 1
                    elif mod_op == oracle_op:
                        print(f"      ⚠️  OPERACIÓN CORRECTA, timestamp diferente")
                    else:
                        print(f"      ❌ OPERACIÓN DIFERENTE")
                else:
                    print(f"   ❌ Doc {documento}: No encontrado")
            
            porcentaje = (homologados / len(documentos_muestra) * 100) if documentos_muestra else 0
            print(f"\n   📊 RESULTADO: {homologados}/{len(documentos_muestra)} ({porcentaje:.1f}%) homologados perfectamente")
            
            if porcentaje == 100:
                print(f"   🏆 ¡MISIÓN NINJA COMPLETADA! 100% HOMOLOGACIÓN LOGRADA")
            elif porcentaje >= 80:
                print(f"   ✅ EXCELENTE PROGRESO - Muy cerca del 100%")
            else:
                print(f"   ⚠️  REQUIERE MÁS AJUSTES")
        else:
            print(f"   ❌ No se encontraron archivos CSV")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🥷 IMPLEMENTACIÓN SOLUCIÓN NINJA FINAL")
    print("=" * 80)
    print("🎯 OBJETIVO: 100% HOMOLOGACIÓN PERFECTA")
    
    # Implementar lógica ninja final
    archivo_ninja = implementar_solucion_ninja_final()
    
    if archivo_ninja:
        print(f"\n🎯 LÓGICA NINJA FINAL IMPLEMENTADA")
        print(f"📄 Archivo: {archivo_ninja}")
        
        # Generar CSV final
        archivos_csv = generar_csv_ninja_final()
        
        if archivos_csv:
            print(f"\n🏆 LÓGICA NINJA FINAL COMPLETADA")
            print(f"✅ Lógica ninja final implementada")
            print(f"✅ CSV generados con homologación ninja")
            
            # Validar homologación final
            validar_homologacion_ninja_final()
            
            print(f"\n🎯 Resultado: Homologación ninja al máximo nivel")
    
    print(f"\n✅ Implementación ninja final completada")
