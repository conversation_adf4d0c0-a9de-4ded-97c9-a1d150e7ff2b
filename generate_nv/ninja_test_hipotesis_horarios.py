#!/usr/bin/env python3
"""
🥷 NINJA TEST - HIPÓTESIS DE LÓGICA POR PRIORIDAD DE HORARIOS
Hipótesis: Oracle usa prioridad por períodos del día + más tardío dentro del período
"""
import pandas as pd
import duckdb
from datetime import datetime

def ninja_test_hipotesis():
    """Test de la hipótesis ninja de prioridad por horarios"""
    print("🥷 NINJA TEST - HIPÓTESIS DE PRIORIDAD POR HORARIOS")
    print("=" * 65)
    
    # Hipótesis: Oracle usa esta lógica de prioridad
    print("🎯 HIPÓTESIS NINJA:")
    print("   1. PRIORIDAD 1: MAÑANA (6:00-11:59) - selecciona MÁS TARDÍO")
    print("   2. PRIORIDAD 2: MADRUGADA (0:00-5:59) - selecciona MÁS TARDÍO") 
    print("   3. PRIORIDAD 3: TARDE (12:00-17:59) - selecciona MÁS TARDÍO")
    print("   4. PRIORIDAD 4: NOCHE (18:00-23:59) - selecciona MÁS TARDÍO")
    
    # Archivos
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    # Documentos de prueba
    documentos_test = ["70635331", "76730654"]
    
    print(f"\n🔍 TESTING HIPÓTESIS CON DOCUMENTOS: {documentos_test}")
    
    for documento in documentos_test:
        print(f"\n{'='*50}")
        print(f"🧪 TEST DOCUMENTO: {documento}")
        print(f"{'='*50}")
        
        # 1. Obtener registros originales
        registros_originales = obtener_registros_originales(log_usr_original, documento)
        
        # 2. Aplicar lógica ninja de prioridad
        registro_ninja = aplicar_logica_ninja(registros_originales)
        
        # 3. Obtener resultado Oracle
        registro_oracle = obtener_resultado_oracle(archivo_oracle, documento)
        
        # 4. Comparar resultados
        comparar_ninja_vs_oracle(documento, registro_ninja, registro_oracle)

def obtener_registros_originales(log_usr_path, documento):
    """Obtiene registros originales con análisis de horarios"""
    try:
        conn = duckdb.connect()
        
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID
        FROM read_parquet('{log_usr_path}')
        WHERE DOCUMENTO = '{documento}'
        ORDER BY CREATEDON
        """
        
        result = conn.execute(query).fetchall()
        
        registros = []
        for requesttype, createdon, userhistid in result:
            dt = datetime.strptime(str(createdon), '%Y-%m-%d %H:%M:%S')
            
            # Clasificar período
            if 6 <= dt.hour < 12:
                periodo = "MAÑANA"
                prioridad = 1
            elif 0 <= dt.hour < 6:
                periodo = "MADRUGADA"
                prioridad = 2
            elif 12 <= dt.hour < 18:
                periodo = "TARDE"
                prioridad = 3
            else:  # 18-23
                periodo = "NOCHE"
                prioridad = 4
            
            registros.append({
                'timestamp': str(createdon),
                'datetime': dt,
                'hora': dt.hour,
                'minuto': dt.minute,
                'segundo': dt.second,
                'periodo': periodo,
                'prioridad': prioridad,
                'requesttype': requesttype,
                'userhistid': userhistid
            })
        
        print(f"   📊 Registros encontrados: {len(registros)}")
        for i, reg in enumerate(registros, 1):
            print(f"      {i}. {reg['timestamp']} ({reg['periodo']}) - Prioridad {reg['prioridad']}")
        
        conn.close()
        return registros
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def aplicar_logica_ninja(registros):
    """Aplica la lógica ninja de prioridad por horarios"""
    if not registros:
        return None
    
    print(f"   🥷 APLICANDO LÓGICA NINJA:")
    
    # Agrupar por prioridad
    por_prioridad = {}
    for reg in registros:
        prioridad = reg['prioridad']
        if prioridad not in por_prioridad:
            por_prioridad[prioridad] = []
        por_prioridad[prioridad].append(reg)
    
    print(f"      📊 Registros por prioridad:")
    for prioridad in sorted(por_prioridad.keys()):
        regs = por_prioridad[prioridad]
        periodo = regs[0]['periodo']
        print(f"         Prioridad {prioridad} ({periodo}): {len(regs)} registros")
    
    # Seleccionar según lógica ninja
    # 1. Tomar la prioridad más alta (número más bajo)
    prioridad_elegida = min(por_prioridad.keys())
    candidatos = por_prioridad[prioridad_elegida]
    
    print(f"      🎯 Prioridad elegida: {prioridad_elegida} ({candidatos[0]['periodo']})")
    
    # 2. Dentro de esa prioridad, tomar el MÁS TARDÍO
    registro_elegido = max(candidatos, key=lambda x: x['datetime'])
    
    print(f"      ✅ Registro ninja seleccionado: {registro_elegido['timestamp']}")
    print(f"         Período: {registro_elegido['periodo']}")
    print(f"         Hora: {registro_elegido['hora']:02d}:{registro_elegido['minuto']:02d}:{registro_elegido['segundo']:02d}")
    
    return registro_elegido

def obtener_resultado_oracle(archivo_oracle, documento):
    """Obtiene el resultado de Oracle"""
    try:
        df = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        reg = df[df['DOCUMENTO'].astype(str) == documento]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            timestamp = registro['FECHA_HORA']
            dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
            
            # Clasificar período Oracle
            if 6 <= dt.hour < 12:
                periodo = "MAÑANA"
            elif 0 <= dt.hour < 6:
                periodo = "MADRUGADA"
            elif 12 <= dt.hour < 18:
                periodo = "TARDE"
            else:
                periodo = "NOCHE"
            
            resultado = {
                'timestamp': timestamp,
                'datetime': dt,
                'periodo': periodo,
                'hora': dt.hour,
                'operacion': registro['OPERACION'],
                'transactionid': registro['TRANSACTIONID']
            }
            
            print(f"   📊 Oracle resultado:")
            print(f"      Timestamp: {timestamp}")
            print(f"      Período: {periodo}")
            print(f"      Hora: {dt.hour:02d}:{dt.minute:02d}:{dt.second:02d}")
            
            return resultado
        else:
            print(f"   ❌ No encontrado en Oracle")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def comparar_ninja_vs_oracle(documento, registro_ninja, registro_oracle):
    """Compara resultado ninja vs Oracle"""
    print(f"   🔍 COMPARACIÓN NINJA VS ORACLE:")
    
    if not registro_ninja or not registro_oracle:
        print(f"      ❌ No se puede comparar - faltan datos")
        return
    
    ninja_timestamp = registro_ninja['timestamp']
    oracle_timestamp = registro_oracle['timestamp']
    
    print(f"      🥷 Ninja:  {ninja_timestamp} ({registro_ninja['periodo']})")
    print(f"      🔮 Oracle: {oracle_timestamp} ({registro_oracle['periodo']})")
    
    if ninja_timestamp == oracle_timestamp:
        print(f"      🏆 ¡HIPÓTESIS NINJA CORRECTA! - MATCH PERFECTO")
        print(f"      ✅ Documento {documento}: Lógica ninja funciona")
    else:
        print(f"      ❌ HIPÓTESIS NINJA INCORRECTA")
        print(f"      ⚠️  Documento {documento}: Lógica ninja NO funciona")
        print(f"      🔍 Diferencia: {ninja_timestamp} != {oracle_timestamp}")

if __name__ == "__main__":
    print("🥷 NINJA TEST - HIPÓTESIS DE PRIORIDAD POR HORARIOS")
    print("=" * 80)
    
    ninja_test_hipotesis()
    
    print(f"\n🎯 RESULTADO NINJA:")
    print(f"   Si ambos documentos dan MATCH PERFECTO,")
    print(f"   entonces hemos encontrado la lógica exacta de Oracle")
    
    print(f"\n✅ Test ninja completado")
