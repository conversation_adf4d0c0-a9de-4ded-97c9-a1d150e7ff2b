#!/usr/bin/env python3
"""
Script para verificar específicamente los registros CHANGE_AUTH_FACTOR
"""
import pandas as pd
import os

def verificar_change_auth_factor():
    """Verifica los registros CHANGE_AUTH_FACTOR en los datos de origen"""
    print("🔍 VERIFICACIÓN DE REGISTROS CHANGE_AUTH_FACTOR")
    print("=" * 55)
    
    documento = "71793435"
    celular = "51907368782"
    
    print(f"🎯 Buscando CHANGE_AUTH_FACTOR para usuario:")
    print(f"   • Documento: {documento}")
    print(f"   • Celular: {celular}")
    
    # 1. Verificar en USER_MODIFICATION_DAY
    print(f"\n1️⃣ VERIFICACIÓN EN USER_MODIFICATION_DAY:")
    verificar_user_modification_day(documento, celular)
    
    # 2. Verificar en datos de origen S3
    print(f"\n2️⃣ VERIFICACIÓN EN DATOS DE ORIGEN S3:")
    verificar_datos_origen_s3(documento, celular)
    
    # 3. Verificar filtros en el pipeline
    print(f"\n3️⃣ VERIFICACIÓN DE FILTROS EN PIPELINE:")
    verificar_filtros_pipeline()

def verificar_user_modification_day(documento, celular):
    """Verifica en el archivo USER_MODIFICATION_DAY"""
    archivo = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_MODIFICATION_DAY.parquet"
    
    if os.path.exists(archivo):
        try:
            df = pd.read_parquet(archivo)
            print(f"   📄 Archivo: USER_MODIFICATION_DAY.parquet")
            print(f"      Total registros: {len(df):,}")
            print(f"      Columnas: {list(df.columns)}")
            
            # Contar tipos de REQUEST_TYPE
            if 'REQUEST_TYPE' in df.columns:
                request_types = df['REQUEST_TYPE'].value_counts()
                print(f"      📊 Tipos de REQUEST_TYPE:")
                for req_type, count in request_types.items():
                    print(f"         {req_type}: {count:,}")
                
                # Buscar específicamente CHANGE_AUTH_FACTOR
                change_auth = df[df['REQUEST_TYPE'] == 'CHANGE_AUTH_FACTOR']
                print(f"      🎯 CHANGE_AUTH_FACTOR: {len(change_auth):,} registros")
                
                if len(change_auth) > 0:
                    print(f"      ✅ SÍ hay registros CHANGE_AUTH_FACTOR")
                    
                    # Mostrar algunos ejemplos
                    print(f"      📋 Primeros registros CHANGE_AUTH_FACTOR:")
                    for i, (_, registro) in enumerate(change_auth.head(5).iterrows(), 1):
                        print(f"         {i}. User_ID: {registro.get('USER_ID', 'N/A')} - Fecha: {registro.get('CREATED_ON', 'N/A')}")
                    
                    # Buscar nuestro usuario específico
                    if 'USER_ID' in df.columns:
                        # Necesitamos encontrar el USER_ID del usuario con documento 71793435
                        # Primero buscar en USER_DATA_TRX
                        user_data_path = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/TEMP_LOGS_USUARIOS/20250609/USER_DATA_TRX.parquet"
                        if os.path.exists(user_data_path):
                            df_user = pd.read_parquet(user_data_path)
                            mask_user = (df_user['ID_VALUE'].astype(str) == documento) & \
                                       (df_user['MSISDN'].astype(str) == celular)
                            usuario_encontrado = df_user[mask_user]
                            
                            if len(usuario_encontrado) > 0:
                                user_id = usuario_encontrado.iloc[0]['USER_ID']
                                print(f"      🔍 USER_ID del usuario: {user_id}")
                                
                                # Buscar CHANGE_AUTH_FACTOR para este USER_ID
                                change_auth_usuario = change_auth[change_auth['USER_ID'] == user_id]
                                print(f"      🎯 CHANGE_AUTH_FACTOR para usuario: {len(change_auth_usuario)}")
                                
                                if len(change_auth_usuario) > 0:
                                    print(f"      ✅ ENCONTRADO CHANGE_AUTH_FACTOR para el usuario!")
                                    for _, reg in change_auth_usuario.iterrows():
                                        print(f"         Fecha: {reg['CREATED_ON']}")
                                        print(f"         Request_Type: {reg['REQUEST_TYPE']}")
                                        print(f"         Old_Data: {reg.get('old_data', 'N/A')}")
                                        print(f"         New_Data: {reg.get('new_data', 'N/A')}")
                                else:
                                    print(f"      ❌ NO hay CHANGE_AUTH_FACTOR para este usuario")
                else:
                    print(f"      ❌ NO hay registros CHANGE_AUTH_FACTOR en el archivo")
            
        except Exception as e:
            print(f"      ❌ Error: {e}")
    else:
        print(f"   ❌ Archivo no encontrado: {archivo}")

def verificar_datos_origen_s3(documento, celular):
    """Verifica en los datos de origen de S3"""
    print(f"   🔍 Verificando datos de origen S3...")
    
    # Verificar si tenemos acceso a los datos de S3 originales
    # Según fuentes_s3.md, USER_MODIFICATION_HISTORY está en:
    # s3://mmoney-datalake-reports-dev/data/user_modification_history/
    
    print(f"   💡 Los datos de origen están en S3:")
    print(f"      s3://mmoney-datalake-reports-dev/data/user_modification_history/")
    print(f"   📋 Para verificar completamente, necesitaríamos:")
    print(f"      1. Acceso directo a S3")
    print(f"      2. Consultar registros con MODIFICATION_TYPE = 'CHANGE_AUTH_FACTOR'")
    print(f"      3. Filtrar por fecha 2025-06-09")
    print(f"      4. Buscar USER_ID correspondiente al documento {documento}")

def verificar_filtros_pipeline():
    """Verifica filtros específicos en el pipeline"""
    print(f"   🔍 Verificando filtros en el pipeline...")
    
    archivo_pipeline = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/pipeline_log_usuarios_duckdb.py"
    
    if os.path.exists(archivo_pipeline):
        with open(archivo_pipeline, 'r') as f:
            contenido = f.read()
        
        # Buscar la consulta de USER_MODIFICATION_DAY
        lineas = contenido.split('\n')
        
        en_user_modification = False
        consulta_lines = []
        
        for i, linea in enumerate(lineas):
            if 'process_sp_user_modification' in linea:
                en_user_modification = True
            elif en_user_modification and 'def ' in linea and 'process_sp_user_modification' not in linea:
                en_user_modification = False
                break
            elif en_user_modification:
                consulta_lines.append((i+1, linea))
        
        if consulta_lines:
            print(f"   📄 Lógica de USER_MODIFICATION encontrada:")
            for linea_num, linea in consulta_lines[:20]:  # Primeras 20 líneas
                if linea.strip():
                    print(f"      {linea_num}: {linea}")
        
        # Buscar filtros específicos
        filtros_encontrados = []
        for i, linea in enumerate(lineas):
            if 'CHANGE_AUTH_FACTOR' in linea:
                filtros_encontrados.append((i+1, linea.strip()))
            elif 'REQUEST_TYPE' in linea and any(palabra in linea.upper() for palabra in ['WHERE', 'AND', 'OR', 'NOT']):
                filtros_encontrados.append((i+1, linea.strip()))
        
        if filtros_encontrados:
            print(f"   🔍 Filtros relacionados con REQUEST_TYPE:")
            for linea_num, linea in filtros_encontrados:
                print(f"      {linea_num}: {linea}")
        else:
            print(f"   ✅ No se encontraron filtros específicos que excluyan CHANGE_AUTH_FACTOR")

def analizar_diferencia_temporal():
    """Analiza si hay diferencia temporal que explique la ausencia"""
    print(f"\n4️⃣ ANÁLISIS DE DIFERENCIA TEMPORAL:")
    
    print(f"   📅 Fechas importantes:")
    print(f"      • Registro original: 2025-06-09 22:52:32")
    print(f"      • Registro en temporales: 2025-06-09 22:50:03")
    print(f"      • Diferencia: ~2.5 minutos")
    
    print(f"   💡 Hipótesis:")
    print(f"      1. El CHANGE_AUTH_FACTOR ocurrió a las 22:52:32")
    print(f"      2. Los datos temporales solo tienen hasta 22:50:03")
    print(f"      3. Posible corte temporal en la extracción de datos")
    print(f"      4. El registro CPIN se perdió por filtro de tiempo")

if __name__ == "__main__":
    print("🕵️ VERIFICACIÓN ESPECÍFICA DE CHANGE_AUTH_FACTOR")
    print("=" * 60)
    
    verificar_change_auth_factor()
    analizar_diferencia_temporal()
    
    print(f"\n📋 CONCLUSIONES:")
    print(f"   1. Si hay CHANGE_AUTH_FACTOR en USER_MODIFICATION_DAY: Problema en procesamiento")
    print(f"   2. Si no hay CHANGE_AUTH_FACTOR: Problema en extracción de datos")
    print(f"   3. Si hay diferencia temporal: Ajustar ventana de extracción")
    print(f"   4. Verificar filtros de fecha/hora en consultas S3")
    
    print(f"\n✅ Verificación completada")
