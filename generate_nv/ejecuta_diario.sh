#!/bin/bash

# Variables de entorno
export PRIVATE_KEY_PATH="/home/<USER>/generate/FileSigner/pdp_sign.key"
export PRIVATE_CRT_PATH="/home/<USER>/generate/FileSigner/SignFileNC.crt"
export OUTPUT_ROUTE="/home/<USER>/output/excel/"
export OUTPUT_ROUTE_CSV="/home/<USER>/output/csv/"

# Definir arrays
values32=("32A")
# Reportes a ejecutar con salida Excel
values=("BITEL-POST" "BITEL-PRE" "SERVICE-PROVIDER" "SERVICIOS-DIRECTOS" "TRAZA-FEE" "RETIROS" "DEPOSITOS")
# Reportes a ejecutar con salida csv
valuescsv=("32A" "BCRP-NETO-EMISORES" "CRANDES-PAGOS" "VALIDAR-BALANCE" "EQUIVALENCIA-LOG-TRX" "EQUIVALENCIA-PAGOS" "EQUIVALENCIA-AHORROS" "BCRP-OPERACIONES-EMISOR" "BCRP-TIPO-CUENTAS" "AZULITO" "UNIQUE" "MOVISTAR" "ENTEL" "RETIRO-SENTINEL" "RETIRO-WU-HUB" "BCRP-BALANCES" "QR-NIUBIZ" "QR-IZIPAY" "MTX-TRANSACTION" "LOG-TRANSACCIONES")
# Reportes a ejecutar con salida csv pero que tienen un flujo adicional
export REPORTS_NO_S3="LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A"

# Definir el tiempo máximo de ejecución (en segundos)
MAX_TIME=1800  # 30 minutos

# Función para ejecutar un proceso con timeout
run_with_timeout() {
    PROCESS_NAME=$1
    SCRIPT=$2
    REPORT=$3
    DATE=$4
    LOG_FILE=$5

    echo "Iniciando: $PROCESS_NAME"

    start_time=$(date +%s)

    python3 $SCRIPT $REPORT $DATE > "$LOG_FILE" 2>&1 &
    pid=$!

    while kill -0 $pid 2>/dev/null; do
        elapsed_time=$(( $(date +%s) - start_time ))
        if [ "$elapsed_time" -gt "$MAX_TIME" ]; then
            echo "El proceso $PROCESS_NAME excedió el tiempo límite de $MAX_TIME segundos. Terminando..."
            kill -9 $pid
            echo "Proceso terminado por timeout después de $elapsed_time segundos" >> "$LOG_FILE"
            break
        fi

        sleep 5
    done

    wait $pid
    return_code=$?

    if [ $return_code -eq 0 ]; then
        echo "✅ $PROCESS_NAME completado exitosamente"
    else
        echo "❌ $PROCESS_NAME falló (código: $return_code)"
    fi
}

ROUTE_CSV="/home/<USER>/output/csv"
TARGET_PATH="/home/<USER>/output/load_rds"

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")

cd /home/<USER>/generate/

# Crear carpetas de logs
mkdir -p logs/{excel,csv,log_transacciones,reports32a_b,account_balances,interope,log_usuarios,prepare,reporte_conciliacion,csv_to_pdf,prepare_rds,mysql_reports}

echo "== Generando REPORTES EXCEL =="
for value in "${values[@]}"; do
    run_with_timeout "$value" "exports_excel/main.py" "$value" "$fecha" "logs/excel/${value}.log"
done

echo "== Generando REPORTES CSV =="
for value in "${valuescsv[@]}"; do
    run_with_timeout "$value" "exports_csv/main.py" "$value" "$fecha" "logs/csv/${value}.log"
done

echo "== Procesando LOG TRANSACCIONES =="
cd /home/<USER>/generate/log_transacciones/
mkdir -p output/"$fecha_path"
run_with_timeout "LOG-TRANSACCIONES-PROCESAR" "procesar.py" "" "$fecha" "/home/<USER>/generate/logs/log_transacciones/LOG-TRANSACCIONES.log"

echo "== Procesando ACCOUNT BALANCES =="
cd /home/<USER>/generate/account_balance/
run_with_timeout "ACCOUNT-BALANCES" "main.py" "" "$fecha" "/home/<USER>/generate/logs/account_balances/ACC-BALANCES.log"

echo "== Generando REPORTES 32x =="
cd /home/<USER>/generate/reports32a-b/
for value in "${values32[@]}"; do
    echo "Iniciando: $value"
    run_with_timeout "$value-32" "main.py" "$value" "$fecha" "/home/<USER>/generate/logs/reports32a_b/${value}.log"
done

echo "== Ejecutando GOPAY =="
cd /home/<USER>/generate/mysql_reports/GOPAY
run_with_timeout "GOPAY" "main.py" "" "$fecha" "/home/<USER>/generate/logs/mysql_reports/GOPAY.log"

echo "== Ejecutando FULLCARGAS =="
cd /home/<USER>/generate/mysql_reports/Fullcargas
run_with_timeout "FULLCARGAS" "main.py" "" "$fecha" "/home/<USER>/generate/logs/mysql_reports/FULLCARGAS.log"

echo "== Ejecutando SERVICIOS-WU =="
cd /home/<USER>/generate/mysql_reports/Servicios-WU
run_with_timeout "SERVICIOS-WU" "main.py" "" "$fecha" "/home/<USER>/generate/logs/mysql_reports/SERVICIOS-WU.log"

echo "== Ejecutando CONCILIACION BIM =="
cd /home/<USER>/generate/reporte_conciliacion/
run_with_timeout "CONCILIACION-BIM" "main.py" "" "$fecha" "/home/<USER>/generate/logs/reporte_conciliacion/CONCILIACION-BIM.log"


echo "== Ejecutando REPORTE INTEROPERABILIDAD NIUBIZ =="
# Archivos especiales
log_trx_file="TR-${fecha_path}.csv"
mtx_trx_header_file="MTX_TRANSACTION_HEADER_${fecha_path}.csv"
log_trx_new_name="LOG_TRX_FINAL.csv"
mtx_trx_new_name="MTX_TRANSACTION_HEADER.csv"
mkdir -p "$TARGET_PATH"

# Mover archivos si existen
if [ -f "$ROUTE_CSV/$log_trx_file" ]; then
    mv "$ROUTE_CSV/$log_trx_file" "$TARGET_PATH/$log_trx_new_name"
    echo "✅ Archivo $log_trx_file movido a $log_trx_new_name"
else
    echo "❌ No se encontro el archivo $log_trx_file"
fi

if [ -f "$ROUTE_CSV/$mtx_trx_header_file" ]; then
    mv "$ROUTE_CSV/$mtx_trx_header_file" "$TARGET_PATH/$mtx_trx_new_name"
    echo "✅ Archivo $mtx_trx_header_file movido a $mtx_trx_new_name"
else
    echo "❌ No se encontro el archivo $mtx_trx_header_file"
fi

cd /home/<USER>/generate/prepare_rds/
run_with_timeout "CARGAR-RDS" "read_csv_sql.py" "$TARGET_PATH" "" "/home/<USER>/generate/logs/prepare_rds/CARGAR-RDS.log"

#cd /home/<USER>/generate/rep_interop/
#python3 application.py "$fecha" > "/home/<USER>/generate/logs/interope/INTEROPE-NIUBIZ.log" 2>&1 &
#wait

# 🔍 Resumen final de logs
cd /home/<USER>/generate/
echo ""
echo "== RESUMEN DE ESTADO =="
for value in "${values[@]}"; do
    if grep -qi "error" "logs/excel/${value}.log"; then
        echo "❌ $value (ver logs/excel/${value}.log)"
    else
        echo "✅ $value"
    fi
done

for value in "${valuescsv[@]}"; do
    if grep -qi "error" "logs/csv/${value}.log"; then
        echo "❌ $value (ver logs/csv/${value}.log)"
    else
        echo "✅ $value"
    fi
done

echo "== FIN DE EJECUCIÓN =="

