#!/usr/bin/env python3
"""
Script para buscar y analizar el archivo original del flujo Oracle
"""
import os
import glob
import pandas as pd
from pathlib import Path

def buscar_archivos_originales():
    """Busca archivos del flujo original en diferentes ubicaciones posibles"""
    print("🔍 BÚSQUEDA DE ARCHIVOS ORIGINALES DEL FLUJO ORACLE")
    print("=" * 60)
    
    # Posibles ubicaciones del archivo original
    rutas_posibles = [
        "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610*.csv",
        "/home/<USER>/generate/log_usuarios/output/LOGUSR-*-20250610*.csv",
        "/home/<USER>/generate/log_usuarios/output/*.csv",
        "/home/<USER>/generate/*/LOGUSR-*-20250610*.csv",
        "/home/<USER>/*/LOGUSR-*-20250610*.csv",
        "/home/<USER>/LOGUSR-*-20250610*.csv",
        "*/LOGUSR-*-20250610*.csv",
        "/tmp/LOGUSR-*-20250610*.csv",
        "/var/tmp/LOGUSR-*-20250610*.csv"
    ]
    
    archivos_encontrados = []
    
    for patron in rutas_posibles:
        try:
            archivos = glob.glob(patron, recursive=True)
            if archivos:
                print(f"✅ Encontrados en {patron}:")
                for archivo in archivos:
                    print(f"   📄 {archivo}")
                    archivos_encontrados.extend(archivos)
        except Exception as e:
            print(f"❌ Error buscando en {patron}: {e}")
    
    # Buscar también archivos con fecha 20250609 (por si hay diferencia de día)
    rutas_alternativas = [
        "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250609*.csv",
        "/home/<USER>/generate/log_usuarios/output/LOGUSR-*-20250609*.csv",
    ]
    
    print(f"\n🔍 Búsqueda alternativa con fecha 20250609:")
    for patron in rutas_alternativas:
        try:
            archivos = glob.glob(patron, recursive=True)
            if archivos:
                print(f"✅ Encontrados en {patron}:")
                for archivo in archivos:
                    print(f"   📄 {archivo}")
                    archivos_encontrados.extend(archivos)
        except Exception as e:
            print(f"❌ Error buscando en {patron}: {e}")
    
    return archivos_encontrados

def verificar_directorios():
    """Verifica qué directorios existen"""
    print(f"\n📁 VERIFICACIÓN DE DIRECTORIOS:")
    
    directorios_verificar = [
        "/home/<USER>",
        "/home/<USER>/generate",
        "/home/<USER>/generate/log_usuarios",
        "/home/<USER>/generate/log_usuarios/output"
    ]
    
    for directorio in directorios_verificar:
        if os.path.exists(directorio):
            print(f"✅ {directorio} - EXISTE")
            try:
                contenido = os.listdir(directorio)
                print(f"   📂 Contenido: {contenido[:10]}{'...' if len(contenido) > 10 else ''}")
            except Exception as e:
                print(f"   ❌ Error listando contenido: {e}")
        else:
            print(f"❌ {directorio} - NO EXISTE")

def analizar_archivo_si_existe(archivo_path):
    """Analiza un archivo si existe"""
    if not os.path.exists(archivo_path):
        print(f"❌ Archivo no encontrado: {archivo_path}")
        return None
    
    try:
        print(f"\n📊 ANÁLISIS DEL ARCHIVO ORIGINAL: {archivo_path}")
        df = pd.read_csv(archivo_path, header=None)
        
        print(f"   • Total de registros: {len(df):,}")
        print(f"   • Total de columnas: {len(df.columns)}")
        
        # Mostrar primeros registros
        print(f"\n📄 PRIMEROS 3 REGISTROS:")
        print(df.head(3).to_string(index=False))
        
        return df
        
    except Exception as e:
        print(f"❌ Error analizando archivo: {e}")
        return None

def comparar_archivos(archivo_modernizado, archivo_original):
    """Compara los dos archivos"""
    print(f"\n🔄 COMPARACIÓN DE ARCHIVOS:")
    print("=" * 40)
    
    # Analizar archivo modernizado
    df_modernizado = pd.read_csv(archivo_modernizado, header=None)
    print(f"📊 Archivo modernizado:")
    print(f"   • Registros: {len(df_modernizado):,}")
    print(f"   • Columnas: {len(df_modernizado.columns)}")
    
    # Analizar archivo original
    df_original = analizar_archivo_si_existe(archivo_original)
    
    if df_original is not None:
        print(f"\n📊 Archivo original:")
        print(f"   • Registros: {len(df_original):,}")
        print(f"   • Columnas: {len(df_original.columns)}")
        
        # Comparación de conteos
        diferencia = len(df_modernizado) - len(df_original)
        print(f"\n🔍 COMPARACIÓN:")
        print(f"   • Diferencia en registros: {diferencia:,}")
        if diferencia == 0:
            print("   ✅ COINCIDENCIA EXACTA EN NÚMERO DE REGISTROS!")
        else:
            print(f"   ❌ NO HAY COINCIDENCIA - Diferencia: {diferencia:,}")

if __name__ == "__main__":
    # Buscar archivos originales
    archivos_encontrados = buscar_archivos_originales()
    
    # Verificar directorios
    verificar_directorios()
    
    # Archivo del pipeline modernizado
    archivo_modernizado = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609074148.csv"
    
    # Archivo original esperado
    archivo_original = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"\n📋 RESUMEN:")
    print(f"   • Archivo modernizado: {archivo_modernizado}")
    print(f"   • Archivo original esperado: {archivo_original}")
    
    if archivos_encontrados:
        print(f"\n✅ Se encontraron {len(archivos_encontrados)} archivos originales")
        for archivo in archivos_encontrados:
            comparar_archivos(archivo_modernizado, archivo)
    else:
        print(f"\n❌ No se encontraron archivos originales para comparar")
        print(f"   • Verificar que el flujo original se haya ejecutado")
        print(f"   • Verificar permisos de acceso a /home/<USER>/")
