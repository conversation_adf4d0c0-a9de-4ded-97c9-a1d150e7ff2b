#!/usr/bin/env python3
"""
Script para generar un archivo Parquet con datos de logs de usuarios utilizando DuckDB.
Este script ejecuta la consulta SQL log_usuarios_all.sql que combina la lógica de los
stored procedures originales (SP_PRE_LOG_USR, SP_USER_MODIFICATION, SP_USER_AUTH_DAY, SP_LOG_USR)
y guarda los resultados en un archivo Parquet.
"""

import os
import sys
import logging
import duckdb
from datetime import datetime
import configparser
import argparse

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Configuración de rutas
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_FILE = os.path.join(BASE_DIR, 'config.ini')
QUERIES_DIR = os.path.join(BASE_DIR, 'queries')
SQL_FILE = os.path.join(QUERIES_DIR, 'log_usuarios_all.sql')

def load_config():
    """Cargar configuración desde el archivo config.ini"""
    if not os.path.exists(CONFIG_FILE):
        logger.error(f"Archivo de configuración no encontrado: {CONFIG_FILE}")
        sys.exit(1)
    
    config = configparser.ConfigParser()
    config.read(CONFIG_FILE)
    return config

def parse_date(date_str):
    """Convertir string de fecha a objeto datetime"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        logger.error(f"Formato de fecha inválido: {date_str}. Debe ser YYYY-MM-DD")
        sys.exit(1)

def read_sql_template():
    """Leer la plantilla SQL desde el archivo"""
    if not os.path.exists(SQL_FILE):
        logger.error(f"Archivo SQL no encontrado: {SQL_FILE}")
        sys.exit(1)
    
    with open(SQL_FILE, 'r', encoding='utf-8') as file:
        return file.read()

def prepare_sql_query(sql_template, config, fecha):
    """Preparar la consulta SQL reemplazando los parámetros"""
    # Extraer componentes de la fecha
    year = fecha.strftime('%Y')
    month = fecha.strftime('%m')
    day = fecha.strftime('%d')
    fecha_inicio = fecha.strftime('%Y-%m-%d')
    
    # Reemplazar parámetros en la consulta SQL
    query = sql_template
    
    # Reemplazar buckets y prefijos de S3
    s3_config = config['s3_buckets']
    for key, value in s3_config.items():
        query = query.replace(f"{{{key}}}", value)
    
    # Reemplazar parámetros de fecha
    query = query.replace('{year}', year)
    query = query.replace('{month}', month)
    query = query.replace('{day}', day)
    query = query.replace('{fecha_inicio}', fecha_inicio)
    
    return query

def execute_query_and_save_parquet(query, output_file):
    """Ejecutar la consulta SQL con DuckDB y guardar los resultados en un archivo Parquet"""
    try:
        # Crear conexión a DuckDB
        conn = duckdb.connect(database=':memory:')
        
        # Configurar DuckDB para acceder a S3
        conn.execute("INSTALL httpfs;")
        conn.execute("LOAD httpfs;")
        
        # Configurar credenciales de AWS si están disponibles en el entorno
        aws_access_key = os.environ.get('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
        aws_session_token = os.environ.get('AWS_SESSION_TOKEN')
        
        if aws_access_key and aws_secret_key:
            conn.execute(f"SET s3_access_key_id='{aws_access_key}';")
            conn.execute(f"SET s3_secret_access_key='{aws_secret_key}';")
            if aws_session_token:
                conn.execute(f"SET s3_session_token='{aws_session_token}';")
        
        # Ejecutar la consulta
        logger.info("Ejecutando consulta SQL...")
        result = conn.execute(query).fetchdf()
        
        # Verificar si se obtuvieron resultados
        if result.empty:
            logger.warning("La consulta no devolvió resultados.")
            return False
        
        # Crear directorio de salida si no existe
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Guardar resultados en archivo Parquet
        logger.info(f"Guardando {len(result)} registros en archivo Parquet: {output_file}")
        result.to_parquet(output_file, index=False)
        
        logger.info("Archivo Parquet generado exitosamente.")
        return True
    
    except Exception as e:
        logger.error(f"Error al ejecutar la consulta o guardar el archivo Parquet: {str(e)}")
        return False

def main():
    """Función principal"""
    # Parsear argumentos de línea de comandos
    parser = argparse.ArgumentParser(description='Generar archivo Parquet con datos de logs de usuarios')
    parser.add_argument('fecha', help='Fecha en formato YYYY-MM-DD')
    args = parser.parse_args()
    
    # Parsear fecha
    fecha = parse_date(args.fecha)
    
    # Cargar configuración
    config = load_config()
    
    # Leer plantilla SQL
    sql_template = read_sql_template()
    
    # Preparar consulta SQL
    query = prepare_sql_query(sql_template, config, fecha)
    
    # Definir archivo de salida
    output_dir = os.path.join('..', '..', 'REPORTE_LOG_USUARIOS', 
                             fecha.strftime('%Y'), 
                             fecha.strftime('%m'), 
                             fecha.strftime('%d'))
    timestamp = datetime.now().strftime('%H%M%S')
    output_file = os.path.join(output_dir, f"REPORTE_LOG_USUARIO_{fecha.strftime('%Y-%m-%d')}_{timestamp}.parquet")
    
    # Ejecutar consulta y guardar resultados
    success = execute_query_and_save_parquet(query, output_file)
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
