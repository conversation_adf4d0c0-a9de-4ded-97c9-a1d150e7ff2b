#!/bin/bash

# Script para ejecutar el proceso de generación de logs de usuarios utilizando archivos Parquet en S3
# Este script es equivalente al ejecuta_log_user.sh original pero adaptado para el nuevo flujo
# que utiliza archivos Parquet en S3 en lugar de tablas de base de datos Oracle y MySQL.

# Configuración de variables
if [ -z "$1" ]; then
    # Si no se proporciona fecha, usar el día anterior
    fecha=$(date -d "yesterday" +"%Y-%m-%d")
else
    # Convertir formato de fecha de YYYY/MM/DD a YYYY-MM-DD si es necesario
    if [[ $1 == *"/"* ]]; then
        fecha=$(date -d "$1" +"%Y-%m-%d")
    else
        fecha=$1
    fi
fi

# Extraer componentes de la fecha
year=$(echo $fecha | cut -d'-' -f1)
month=$(echo $fecha | cut -d'-' -f2)
day=$(echo $fecha | cut -d'-' -f3)

# Configuración de logs y directorios
LOG_DIR="logs"
OUTPUT_DIR="../../REPORTE_LOG_USUARIOS/$year/$month/$day"
LOG_FILE="$LOG_DIR/ejecuta_log_user_parquet.log"
MAX_TIME=1500  # Tiempo máximo de ejecución en segundos
MAX_RETRIES=3  # Número máximo de reintentos

# Crear directorios necesarios
mkdir -p "$LOG_DIR"
mkdir -p "$OUTPUT_DIR"

# Función para registrar mensajes en el log
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Función para ejecutar un proceso con control de tiempo y reintentos
execute_with_retry() {
    local process_name=$1
    local command=$2
    local log_file=$3
    local retry_count=0
    
    log_message "Iniciando proceso: $process_name"
    
    while [ "$retry_count" -lt "$MAX_RETRIES" ]; do
        log_message "Ejecutando $process_name, intento #$((retry_count + 1))"
        
        start_time=$(date +%s)
        
        # Ejecutar el comando en segundo plano
        eval "$command" > "$log_file" 2>&1 &
        pid=$!
        
        # Monitorear el proceso
        while kill -0 $pid 2>/dev/null; do
            elapsed_time=$(($(date +%s) - start_time))
            if [ "$elapsed_time" -gt "$MAX_TIME" ]; then
                log_message "El proceso $process_name excedió el tiempo límite de $MAX_TIME segundos. Reiniciando..."
                kill -9 $pid 2>/dev/null
                break
            fi
            sleep 5
        done
        
        # Esperar a que el proceso termine
        wait $pid
        exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            log_message "$process_name completado exitosamente"
            return 0
        else
            log_message "$process_name falló con código de salida $exit_code"
        fi
        
        retry_count=$((retry_count + 1))
        
        if [ "$retry_count" -lt "$MAX_RETRIES" ]; then
            log_message "Reintentando $process_name..."
        fi
    done
    
    if [ "$retry_count" -ge "$MAX_RETRIES" ]; then
        log_message "El proceso $process_name falló después de $MAX_RETRIES intentos."
        return 1
    fi
}

# Inicio del script
log_message "=== Iniciando proceso de generación de logs de usuarios para la fecha $fecha ==="

# Paso 1: Ejecutar la consulta SQL para generar el archivo Parquet
log_message "Paso 1: Generando archivo Parquet con datos de logs de usuarios"
execute_with_retry "GENERAR-PARQUET" "python3 generar_parquet.py '$fecha'" "$LOG_DIR/generar_parquet.log"
if [ $? -ne 0 ]; then
    log_message "Error en la generación del archivo Parquet. Abortando proceso."
    exit 1
fi

# Paso 2: Post-procesar el archivo Parquet
log_message "Paso 2: Post-procesando archivo Parquet"
execute_with_retry "POST-PROCESS" "python3 post_process.py '$fecha'" "$LOG_DIR/post_process.log"
if [ $? -ne 0 ]; then
    log_message "Error en el post-procesamiento del archivo Parquet. Abortando proceso."
    exit 1
fi

# Paso 3: Procesar y distribuir los logs
log_message "Paso 3: Procesando y distribuyendo logs"
execute_with_retry "PROCESAR-LOGS" "python3 procesar_logs.py '$fecha'" "$LOG_DIR/procesar_logs.log"
if [ $? -ne 0 ]; then
    log_message "Error en el procesamiento y distribución de logs. Abortando proceso."
    exit 1
fi

# Finalización del script
log_message "=== Proceso de generación de logs de usuarios completado exitosamente ==="

exit 0
