#!/bin/bash

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

LOG_FILE="execution_status.log"
MAX_TIME=1200
MAX_RETRIES=3

echo "$fecha"

cd /home/<USER>/generate/

# Crear carpetas de logs
mkdir -p logs/{excel,csv,log_transacciones,reports32a_b,account_balances,log_usuarios,prepare,reporte_conciliacion,csv_to_pdf,prepare_rds,mysql_reports}

echo "== Instalando dependencias si faltan =="
pip install -r requirements.txt

if [ ! -f "$LOG_FILE" ] || [ ! -s "$LOG_FILE" ]; then
	echo "$(date) - Iniciando proceso de ejecucion"
	echo "El archivo de log esta vacio. Iniciando el proceso desde cero."
fi

check_status_and_run() {
	PART_NAME=$1
	RETRY_COUNT=0
	
	while [ "$RETRY_COUNT" -lt "$MAX_RETRIES" ]; do
		echo "$(date) - Ejecutando $PART_NAME, intento #$((RETRY_COUNT + 1))"

		start_time=$(date +%s)

		python3 prepare/main.py "$fecha" "$PART_NAME" > "logs/prepare/$PART_NAME.log"  2>&1 &
		pid=$!

		while kill -0 $pid 2>/dev/null; do
			elapsed_time=$(( $(date +%s) - start_time ))
			if [ "$elapsed_time" -gt "$MAX_TIME" ]; then
				echo "$(date) - El proceso $PART_NAME excedió el tiempo límite de $MAX_TIME segundos. Reiniciando..."
				kill -9 $pid
				break
			fi

			sleep 5

		done

		wait $pid
		if [ $? -eq 0 ]; then
			echo "$(date) - $PART_NAME completado exitosamente"
			return
		else
			echo "$(date) - $PART_NAME falló."
		fi

		RETRY_COUNT=$((RETRY_COUNT + 1))

		if [ "$RETRY_COUNT" -lt "$MAX_RETRIES" ]; then
			echo "$(date) - Reintentando $PART_NAME..."
		fi
	done

	if [ "$RETRY_COUNT" -ge "$MAX_RETRIES" ]; then
		echo "$(date) - El proceso $PART_NAME falló después de 3 intentos."
	fi

}
#Llena los campos ATTR7, ATTR8 (USER_PROFILE) y FIELD7 (Identificador de transaccion de MTX_TRANSACTION_HEADER)
check_status_and_run "POBLAR-NULL"
#Trae la data pasada de los usuarios que hicieron cambio de emisor
check_status_and_run "USER-MODIFY"
#Genere el pre balance (Acumula hasta 31 dias)
check_status_and_run "BALANCE"
#Genera el pre log de transacciones y la tabla final del log de transacciones
check_status_and_run "LOG-TRX"
#check_status_and_run "LOG-USR"

if ! grep -q "PROCESO COMPLETADO" "$LOG_FILE"; then
	echo "$fecha" "- PROCESO COMPLETADO" >> "$LOG_FILE"
fi

echo "Proceso finalizado. Revisa el archivo de log: $LOG_FILE"

