#!/bin/bash

# Variables de entorno
export PRIVATE_KEY_PATH="/home/<USER>/generate/FileSigner/private_key.key"
export PRIVATE_CRT_PATH="/home/<USER>/generate/FileSigner/private_key.crt"
export OUTPUT_ROUTE="/home/<USER>/output/excel/"
export OUTPUT_ROUTE_CSV="/home/<USER>/output/csv/"

# Definir el array con los valores
values=("DEPOSITOS" "RETIROS")
export REPORTS_NO_S3="LOG-TRANSACCIONES,MTX-TRANSACTION,USER-BALANCES,32A"

ROUTE_CSV="/home/<USER>/output/csv"

if [ -z "$1" ]; then
    fecha=$(date -d "yesterday" +"%Y/%m/%d")
else
    fecha=$1
fi

fecha_path=$(date -d "$fecha + 1 day" +"%Y%m%d")

pip install -r requirements.txt

#Nos posicionamos en la raiz
cd /home/<USER>/generate/

# Ejecutar en paralelo
for value in "${values[@]}"; do
    python3 exports_excel/main.py "$value" "$fecha"> "logs/excel/${value}.log" 2>&1 &
done

