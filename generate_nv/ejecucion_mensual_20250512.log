== Instalando dependencias si faltan ==
Requirement already satisfied: pdfkit==1.0.0 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 1)) (1.0.0)
Requirement already satisfied: pymysql==1.0.2 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 2)) (1.0.2)
Requirement already satisfied: openpyxl==3.1.5 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 3)) (3.1.5)
Requirement already satisfied: xlwt==1.3.0 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 4)) (1.3.0)
Requirement already satisfied: xlrd<2.0.0 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 5)) (1.2.0)
Requirement already satisfied: pandas==2.2.3 in /usr/local/lib64/python3.9/site-packages (from -r requirements.txt (line 6)) (2.2.3)
Requirement already satisfied: cryptography==44.0.2 in /usr/local/lib64/python3.9/site-packages (from -r requirements.txt (line 7)) (44.0.2)
Requirement already satisfied: boto3==1.37.16 in /usr/local/lib/python3.9/site-packages (from -r requirements.txt (line 8)) (1.37.16)
Requirement already satisfied: et-xmlfile in /usr/local/lib/python3.9/site-packages (from openpyxl==3.1.5->-r requirements.txt (line 3)) (2.0.0)
Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2025.1)
Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in /usr/lib/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2022.7.1)
Requirement already satisfied: numpy>=1.22.4 in /usr/local/lib64/python3.9/site-packages (from pandas==2.2.3->-r requirements.txt (line 6)) (2.0.2)
Requirement already satisfied: cffi>=1.12 in /usr/lib64/python3.9/site-packages (from cryptography==44.0.2->-r requirements.txt (line 7)) (1.14.5)
Requirement already satisfied: s3transfer<0.12.0,>=0.11.0 in /usr/local/lib/python3.9/site-packages (from boto3==1.37.16->-r requirements.txt (line 8)) (0.11.4)
Requirement already satisfied: jmespath<2.0.0,>=0.7.1 in /usr/lib/python3.9/site-packages (from boto3==1.37.16->-r requirements.txt (line 8)) (0.10.0)
Requirement already satisfied: botocore<1.38.0,>=1.37.16 in /usr/local/lib/python3.9/site-packages (from boto3==1.37.16->-r requirements.txt (line 8)) (1.37.16)
Requirement already satisfied: urllib3<1.27,>=1.25.4 in /usr/lib/python3.9/site-packages (from botocore<1.38.0,>=1.37.16->boto3==1.37.16->-r requirements.txt (line 8)) (1.25.10)
Requirement already satisfied: pycparser in /usr/lib/python3.9/site-packages (from cffi>=1.12->cryptography==44.0.2->-r requirements.txt (line 7)) (2.20)
Requirement already satisfied: six>=1.5 in /usr/lib/python3.9/site-packages (from python-dateutil>=2.8.2->pandas==2.2.3->-r requirements.txt (line 6)) (1.15.0)
Requirement already satisfied: ply==3.11 in /usr/lib/python3.9/site-packages (from pycparser->cffi>=1.12->cryptography==44.0.2->-r requirements.txt (line 7)) (3.11)
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
== Generando REPORTES EXCEL ==
Iniciando: COMISIONES-BIMER
Iniciando: P2P
Iniciando: CASHIN
Iniciando: CASHOUT
Iniciando: AGENTES-BIM
Iniciando: SERVICIOS-DIRECTOS
Iniciando: COMERCIOS
Iniciando: RECARGAS
== Generando REPORTES CSV ==
Iniciando: INTEROPE-COBRAR-PDF
Iniciando: INTEROPE-PAGAR-PDF
Iniciando: INTEROPE-COBRAR
Iniciando: INTEROPE-PAGAR
Iniciando: 32B-I
Iniciando: 32B-II
Iniciando: 32B-III
Iniciando: 32B-IV
Iniciando: 32B-V
Iniciando: COMISIONES
== Generando REPORTES 32x ==
Iniciando: 32B-I
Iniciando: 32B-II
Iniciando: 32B-III
Iniciando: 32B-IV
Iniciando: 32B-V
== Ejecutando CREACION DE PDFS ==

== RESUMEN DE ESTADO ==
grep: logs/excel/COMISIONES-BIMER.log: No such file or directory
✅ COMISIONES-BIMER
grep: logs/excel/P2P.log: No such file or directory
✅ P2P
grep: logs/excel/CASHIN.log: No such file or directory
✅ CASHIN
grep: logs/excel/CASHOUT.log: No such file or directory
✅ CASHOUT
grep: logs/excel/AGENTES-BIM.log: No such file or directory
✅ AGENTES-BIM
grep: logs/excel/SERVICIOS-DIRECTOS.log: No such file or directory
✅ SERVICIOS-DIRECTOS
grep: logs/excel/COMERCIOS.log: No such file or directory
✅ COMERCIOS
grep: logs/excel/RECARGAS.log: No such file or directory
✅ RECARGAS
grep: logs/csv/INTEROPE-COBRAR-PDF.log: No such file or directory
✅ INTEROPE-COBRAR-PDF
grep: logs/csv/INTEROPE-PAGAR-PDF.log: No such file or directory
✅ INTEROPE-PAGAR-PDF
grep: logs/csv/INTEROPE-COBRAR.log: No such file or directory
✅ INTEROPE-COBRAR
grep: logs/csv/INTEROPE-PAGAR.log: No such file or directory
✅ INTEROPE-PAGAR
grep: logs/csv/32B-I.log: No such file or directory
✅ 32B-I
grep: logs/csv/32B-II.log: No such file or directory
✅ 32B-II
grep: logs/csv/32B-III.log: No such file or directory
✅ 32B-III
grep: logs/csv/32B-IV.log: No such file or directory
✅ 32B-IV
grep: logs/csv/32B-V.log: No such file or directory
✅ 32B-V
grep: logs/csv/COMISIONES.log: No such file or directory
✅ COMISIONES
grep: logs/reports32a_b/32B-I.log: No such file or directory
✅ 32B-I
grep: logs/reports32a_b/32B-II.log: No such file or directory
✅ 32B-II
grep: logs/reports32a_b/32B-III.log: No such file or directory
✅ 32B-III
grep: logs/reports32a_b/32B-IV.log: No such file or directory
✅ 32B-IV
grep: logs/reports32a_b/32B-V.log: No such file or directory
✅ 32B-V
== FIN DE EJECUCIÓN ==
