# -*- coding: utf-8 -*-
"""
Created on Wed Jan 29 18:21:09 2025

@author: hfernan<PERSON>
"""

import logging
from logging.handlers import TimedRotatingFileHandler
import os
import sys
import boto3
import re
import pandas as pd
from datetime import datetime, timedelta
from config import bucket_s3,input_path, private_key_path, public_key_path
from file_signer.file_signer import FileSigner

# Obtener la fecha de ayer en formato 'YYYYMMDD'
full_datetime = (datetime.now() + timedelta(days=0))
process_time = full_datetime.strftime('%H%M%S')

input_date = sys.argv[2]
data_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=1)
file_day = datetime.strptime(input_date, '%Y/%m/%d') + timedelta(days=0)
#data_day = full_datetime.strftime('%Y%m%d')
data_month = data_day.strftime('%Y%m')
path_day = data_day.strftime('%Y-%m-%d')
#data_day = data_day.strftime('%Y%m%d')
file_data_day = data_day.strftime('%Y%m%d')
data_day = data_day.strftime('%Y%m%d')

log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_filename = os.path.join(log_dir, 'output.log')
handler = TimedRotatingFileHandler(log_filename, when="midnight", interval=1)
handler.suffix = "%Y%m%d"
logging.basicConfig(level=logging.INFO, handlers=[handler])

def upload_s3(file_route, file_name, s3_key):
    s3_client = boto3.client('s3')
    s3_client.upload_file(file_route, bucket_s3, f"{s3_key}{file_name}")
    logging.info("Archivo: %s subido a: s3://%s", file_name, f"{bucket_s3}/{s3_key}{file_name}")

if len(sys.argv) < 2:
    print("Debe especificar un parámetro, como '32B' y su correlativo en romano o '32A'.")
    sys.exit(1)

param = sys.argv[1].upper()  # Convertimos el parámetro a mayúsculas para asegurar consistencia
# Construir el nombre del archivo de entrada
if param == "32A":

    input_file = f"{input_path}/32A-{data_day}.csv"
    cod_identificador = "0232"
    codigo_anexo = "01"
    codigo_sbs = "0SBSCODE"
    fecha_reporte = file_data_day
    codigo_expr = "012"
    dato_control = "000000000000000"
    pref_output_name = "REPORTE32A"
    output = "output-32A/"
    output_day = file_data_day + process_time
    header = cod_identificador + codigo_anexo + codigo_sbs + fecha_reporte + codigo_expr + dato_control
        
elif param == "32B-III":
    
    input_file = f"{input_path}/32B-III-{data_day}.csv"
    cod_identificador = "0232"
    codigo_anexo = "04"
    codigo_sbs = "0SBSCODE"
    fecha_reporte = data_day
    codigo_expr = "010"
    dato_control = "000000000000000"
    pref_output_name = "REPORTE32B-III"
    output = "output-32B-III/"
    output_day = data_month

    header = cod_identificador + codigo_anexo + codigo_sbs + fecha_reporte + codigo_expr + dato_control
    
elif param == "32B-II":
    
    input_file = f"{input_path}/32B-II-{data_day}.csv"
    cod_identificador = "0232"
    codigo_anexo = "03"
    codigo_sbs = "0SBSCODE"
    fecha_reporte = data_day
    codigo_expr = "012"
    dato_control = "000000000000000"
    pref_output_name = "REPORTE32B-II"
    output = "output-32B-II/"
    output_day = data_month

    header = cod_identificador + codigo_anexo + codigo_sbs + fecha_reporte + codigo_expr + dato_control
    
elif param == "32B-IV":
    
    input_file = f"{input_path}/32B-IV-{data_day}.csv"
    cod_identificador = "0232"
    codigo_anexo = "05"
    codigo_sbs = "0SBSCODE"
    fecha_reporte = data_day
    codigo_expr = "012"
    dato_control = "000000000000000"
    pref_output_name = "REPORTE32B-IV"
    output = "output-32B-IV/"
    output_day = data_month

    header = cod_identificador + codigo_anexo + codigo_sbs + fecha_reporte + codigo_expr + dato_control

elif param == "32B-V":
    
    input_file = f"{input_path}/32B-V-{data_day}.csv"
    cod_identificador = "0232"
    codigo_anexo = "06"
    codigo_sbs = "0SBSCODE"
    fecha_reporte = data_day
    codigo_expr = "010"
    dato_control = "000000000000000"
    pref_output_name = "REPORTE32B-V"
    output = "output-32B-V/"
    output_day = data_month

    header = cod_identificador + codigo_anexo + codigo_sbs + fecha_reporte + codigo_expr + dato_control

elif param == "32B-I":
    
    input_file = f"{input_path}/32B-I-{data_day}.csv"
    cod_identificador = "0232"
    codigo_anexo = "02"
    codigo_sbs = "0SBSCODE"
    fecha_reporte = data_day
    codigo_expr = "012"
    dato_control = "000000000000000"
    pref_output_name = "REPORTE32B-I"
    output = "output-32B-I/"
    output_day = data_month

    header = cod_identificador + codigo_anexo + codigo_sbs + fecha_reporte + codigo_expr + dato_control
else:
    print("Parámetro no reconocido.")
    sys.exit(1)
    

# Leer el archivo CSV original
df = pd.read_csv(input_file, dtype=str)

# Leer el archivo codigos_sbs.csv
codigos_sbs_df = pd.read_csv('codigos_sbs.csv', dtype=str)

for emisor in df['EMISOR'].unique():
    # Filtrar el dataframe por el valor actual de 'emisor'
    df_emisor = df[df['EMISOR'] == emisor]
    
    emisor_path = re.sub(r'[^A-Za-z]', '', emisor)  
    # Eliminar la columna 'emisor' antes de escribir el archivo
    df_emisor = df_emisor.drop(columns=['EMISOR'])
    
    # Ordenar el dataframe por la segunda columna (índice 1)
    df_emisor = df_emisor.sort_values(by=df_emisor.columns[0], ascending=True)  # Ordenar ascendentemente
    
    # Obtener el valor de CODIGOSBS para el 'emisor' correspondiente
    codigos_sbs_value = codigos_sbs_df[codigos_sbs_df['EMISOR'] == emisor]['CODIGOSBS'].values
    
    if codigos_sbs_value.size > 0:
        codigos_sbs_value = codigos_sbs_value[0]
    else:
        codigos_sbs_value = "Codigo SBS no registrado"
        
    # Reemplazar CODIGOSBS en el encabezado
    header_modified = header.replace('SBSCODE', codigos_sbs_value)
    
    # Crear el nombre del archivo (por ejemplo: TR-emisor)
    filename = output + f"{pref_output_name}-{emisor}-{output_day}.txt"
    
    # Abrir el archivo en modo de escritura
    with open(filename, 'w') as f:
        # Escribir el encabezado pero primero reemplazar el encabezado por el CODIGOSBS correspondiente
        
        f.write(header_modified + '\n')
        
        # Escribir los registros del dataframe
        for index, row in df_emisor.iterrows():
            # Convertir cada fila a una cadena, separada por tabulaciones
            row_str = ''.join(str(value) for value in row)  # Separa los valores por tabulaciones
            f.write(row_str + '\n')

    print(f"Archivo {filename} creado con éxito.")

    s3_key = f"{emisor}/{path_day}/{param}/"
    upload_s3(filename, os.path.basename(filename), s3_key)


    #Firmado de archivos:
    signer = FileSigner(private_key_path)
    signer.sign_file(filename)
    signature_path = f"{filename}.signature"
    is_valid = signer.verify_signature(filename, signature_path, public_key_path)
    signature_file = os.path.basename(filename) + ".signature"
    if is_valid:
        upload_s3(signature_path, signature_file, s3_key)

