#!/usr/bin/env python3
"""
🥷 INVESTIGACIÓN PROFUNDA ORACLE - ENCONTRAR LÓGICA EXACTA
Analizar múltiples documentos para encontrar el patrón real de Oracle
"""
import pandas as pd
import duckdb
from datetime import datetime

def ninja_investigacion_profunda():
    """Investigación profunda para encontrar la lógica exacta de Oracle"""
    print("🥷 INVESTIGACIÓN PROFUNDA ORACLE - LÓGICA EXACTA")
    print("=" * 60)
    
    # Documentos de prueba con diferentes comportamientos
    documentos_test = ["70635331", "76730654", "60918019"]
    
    # Archivos
    log_usr_original = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/LOG_USR.parquet"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    print(f"🎯 Documentos a analizar: {documentos_test}")
    
    # Ana<PERSON>zar cada documento en detalle
    patrones_oracle = {}
    
    for documento in documentos_test:
        print(f"\n{'='*50}")
        print(f"🔍 ANÁLISIS PROFUNDO DOCUMENTO: {documento}")
        print(f"{'='*50}")
        
        patron = analizar_documento_profundo(log_usr_original, archivo_oracle, documento)
        patrones_oracle[documento] = patron
    
    # Buscar patrón común
    print(f"\n{'='*60}")
    print(f"🧠 ANÁLISIS DE PATRONES COMUNES")
    print(f"{'='*60}")
    
    buscar_patron_comun(patrones_oracle)

def analizar_documento_profundo(log_usr_path, archivo_oracle, documento):
    """Análisis profundo de un documento específico"""
    
    # 1. Obtener resultado Oracle
    oracle_result = obtener_oracle_result(archivo_oracle, documento)
    
    # 2. Obtener todos los registros originales
    registros_originales = obtener_registros_detallados(log_usr_path, documento)
    
    # 3. Analizar qué registro eligió Oracle
    patron = analizar_seleccion_oracle(registros_originales, oracle_result)
    
    return patron

def obtener_oracle_result(archivo_oracle, documento):
    """Obtiene el resultado de Oracle para el documento"""
    try:
        df = pd.read_csv(archivo_oracle, header=None)
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        df.columns = columnas[:len(df.columns)]
        
        reg = df[df['DOCUMENTO'].astype(str) == documento]
        
        if len(reg) > 0:
            registro = reg.iloc[0]
            timestamp = registro['FECHA_HORA']
            dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
            
            resultado = {
                'timestamp': timestamp,
                'datetime': dt,
                'hora': dt.hour,
                'minuto': dt.minute,
                'segundo': dt.second,
                'operacion': registro['OPERACION']
            }
            
            print(f"   📊 Oracle selecciona: {timestamp}")
            return resultado
        else:
            print(f"   ❌ No encontrado en Oracle")
            return None
            
    except Exception as e:
        print(f"   ❌ Error Oracle: {e}")
        return None

def obtener_registros_detallados(log_usr_path, documento):
    """Obtiene registros detallados con análisis completo"""
    try:
        conn = duckdb.connect()
        
        query = f"""
        SELECT REQUESTTYPE, CREATEDON, USERHISTID, MSISDN, BANKDOMAIN
        FROM read_parquet('{log_usr_path}')
        WHERE DOCUMENTO = '{documento}'
        ORDER BY CREATEDON
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"   📊 Total registros originales: {len(result)}")
        
        registros = []
        for i, (requesttype, createdon, userhistid, msisdn, bankdomain) in enumerate(result, 1):
            dt = datetime.strptime(str(createdon), '%Y-%m-%d %H:%M:%S')
            
            registro = {
                'orden': i,
                'timestamp': str(createdon),
                'datetime': dt,
                'hora': dt.hour,
                'minuto': dt.minute,
                'segundo': dt.second,
                'requesttype': requesttype,
                'userhistid': userhistid,
                'msisdn': msisdn,
                'bankdomain': bankdomain
            }
            
            registros.append(registro)
            print(f"      {i}. {createdon} - {requesttype} - {userhistid}")
        
        conn.close()
        return registros
        
    except Exception as e:
        print(f"   ❌ Error registros: {e}")
        return []

def analizar_seleccion_oracle(registros, oracle_result):
    """Analiza qué registro seleccionó Oracle y por qué"""
    
    if not oracle_result or not registros:
        return None
    
    oracle_timestamp = oracle_result['timestamp']
    
    # Encontrar el registro que coincide con Oracle
    registro_oracle = None
    posicion_oracle = None
    
    for i, reg in enumerate(registros):
        if reg['timestamp'] == oracle_timestamp:
            registro_oracle = reg
            posicion_oracle = i + 1
            break
    
    if not registro_oracle:
        print(f"   ❌ No se encontró el registro Oracle en los datos originales")
        return None
    
    print(f"   🎯 Oracle eligió el registro #{posicion_oracle} de {len(registros)}")
    print(f"      Timestamp: {oracle_timestamp}")
    print(f"      UserHistID: {registro_oracle['userhistid']}")
    
    # Analizar patrones
    patron = {
        'documento': oracle_result.get('documento', ''),
        'total_registros': len(registros),
        'posicion_elegida': posicion_oracle,
        'timestamp_elegido': oracle_timestamp,
        'es_primero': posicion_oracle == 1,
        'es_ultimo': posicion_oracle == len(registros),
        'es_medio': 1 < posicion_oracle < len(registros),
        'hora_elegida': registro_oracle['hora'],
        'userhistid_elegido': registro_oracle['userhistid']
    }
    
    # Analizar si hay patrón por posición
    if patron['es_primero']:
        print(f"   📊 Patrón: Oracle elige el PRIMERO (más antiguo)")
    elif patron['es_ultimo']:
        print(f"   📊 Patrón: Oracle elige el ÚLTIMO (más reciente)")
    else:
        print(f"   📊 Patrón: Oracle elige el #{posicion_oracle} (ni primero ni último)")
    
    # Analizar si hay patrón por hora
    horas_disponibles = [r['hora'] for r in registros]
    hora_min = min(horas_disponibles)
    hora_max = max(horas_disponibles)
    
    if registro_oracle['hora'] == hora_min:
        print(f"   📊 Patrón: Oracle elige la HORA MÁS TEMPRANA ({registro_oracle['hora']})")
        patron['prefiere_hora_temprana'] = True
    elif registro_oracle['hora'] == hora_max:
        print(f"   📊 Patrón: Oracle elige la HORA MÁS TARDÍA ({registro_oracle['hora']})")
        patron['prefiere_hora_tardia'] = True
    else:
        print(f"   📊 Patrón: Oracle elige hora intermedia ({registro_oracle['hora']})")
        patron['prefiere_hora_intermedia'] = True
    
    # Analizar si hay patrón por UserHistID
    userhistids = [r['userhistid'] for r in registros]
    userhistids_unicos = list(set(userhistids))
    
    if len(userhistids_unicos) > 1:
        print(f"   📊 UserHistIDs disponibles: {userhistids_unicos}")
        print(f"   📊 Oracle eligió UserHistID: {registro_oracle['userhistid']}")
        
        # Verificar si Oracle prefiere algún UserHistID específico
        if registro_oracle['userhistid'] == min(userhistids_unicos):
            print(f"   📊 Patrón: Oracle prefiere UserHistID MÁS PEQUEÑO")
            patron['prefiere_userhistid_menor'] = True
        elif registro_oracle['userhistid'] == max(userhistids_unicos):
            print(f"   📊 Patrón: Oracle prefiere UserHistID MÁS GRANDE")
            patron['prefiere_userhistid_mayor'] = True
    
    return patron

def buscar_patron_comun(patrones_oracle):
    """Busca patrones comunes entre todos los documentos"""
    
    print(f"   🧠 ANÁLISIS DE PATRONES COMUNES:")
    
    documentos = list(patrones_oracle.keys())
    
    # Analizar patrones de posición
    primeros = sum(1 for p in patrones_oracle.values() if p and p.get('es_primero', False))
    ultimos = sum(1 for p in patrones_oracle.values() if p and p.get('es_ultimo', False))
    medios = sum(1 for p in patrones_oracle.values() if p and p.get('es_medio', False))
    
    print(f"      📊 Patrones de posición:")
    print(f"         Elige PRIMERO: {primeros}/{len(documentos)} documentos")
    print(f"         Elige ÚLTIMO: {ultimos}/{len(documentos)} documentos")
    print(f"         Elige MEDIO: {medios}/{len(documentos)} documentos")
    
    # Analizar patrones de hora
    hora_temprana = sum(1 for p in patrones_oracle.values() if p and p.get('prefiere_hora_temprana', False))
    hora_tardia = sum(1 for p in patrones_oracle.values() if p and p.get('prefiere_hora_tardia', False))
    
    print(f"      📊 Patrones de hora:")
    print(f"         Prefiere HORA TEMPRANA: {hora_temprana}/{len(documentos)} documentos")
    print(f"         Prefiere HORA TARDÍA: {hora_tardia}/{len(documentos)} documentos")
    
    # Analizar patrones de UserHistID
    userhistid_menor = sum(1 for p in patrones_oracle.values() if p and p.get('prefiere_userhistid_menor', False))
    userhistid_mayor = sum(1 for p in patrones_oracle.values() if p and p.get('prefiere_userhistid_mayor', False))
    
    print(f"      📊 Patrones de UserHistID:")
    print(f"         Prefiere UserHistID MENOR: {userhistid_menor}/{len(documentos)} documentos")
    print(f"         Prefiere UserHistID MAYOR: {userhistid_mayor}/{len(documentos)} documentos")
    
    # Conclusión
    print(f"\n   🎯 CONCLUSIÓN NINJA:")
    
    if primeros == len(documentos):
        print(f"      ✅ Oracle SIEMPRE elige el PRIMERO (más antiguo)")
        print(f"      🔧 SOLUCIÓN: Usar ORDER BY CREATEDON ASC")
    elif ultimos == len(documentos):
        print(f"      ✅ Oracle SIEMPRE elige el ÚLTIMO (más reciente)")
        print(f"      🔧 SOLUCIÓN: Usar ORDER BY CREATEDON DESC")
    elif hora_temprana == len(documentos):
        print(f"      ✅ Oracle SIEMPRE elige la HORA MÁS TEMPRANA")
        print(f"      🔧 SOLUCIÓN: Usar ORDER BY hora ASC, CREATEDON ASC")
    elif userhistid_menor == len(documentos):
        print(f"      ✅ Oracle SIEMPRE elige el UserHistID MÁS PEQUEÑO")
        print(f"      🔧 SOLUCIÓN: Usar ORDER BY USERHISTID ASC")
    else:
        print(f"      ⚠️  Oracle usa LÓGICA COMPLEJA - requiere más análisis")
        print(f"      🔧 SOLUCIÓN: Investigar más documentos o factores adicionales")

if __name__ == "__main__":
    print("🥷 INVESTIGACIÓN PROFUNDA ORACLE")
    print("=" * 80)
    
    ninja_investigacion_profunda()
    
    print(f"\n🎯 OBJETIVO: Encontrar la lógica exacta de Oracle")
    print(f"📋 RESULTADO: Patrón común identificado para corrección")
    
    print(f"\n✅ Investigación profunda completada")
