+ export PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/private_key.key
+ PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/private_key.key
+ export PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/private_key.crt
+ PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/private_key.crt
+ export OUTPUT_ROUTE=/home/<USER>/output/excel/
+ OUTPUT_ROUTE=/home/<USER>/output/excel/
+ export OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ valuescsv=("LOG-USUARIOS")
+ export REPORTS_NO_S3=LOG-USUARIOS
+ REPORTS_NO_S3=LOG-USUARIOS
+ ROUTE_CSV=/home/<USER>/output/csv
+ '[' -z 2025/04/10 ']'
+ fecha=2025/04/10
++ date -d '2025/04/10 + 1 day' +%Y%m%d
+ fecha_path=20250411
+ cd /home/<USER>/generate/
+ cd /home/<USER>/generate/log_usuarios/
+ wait
+ python3 procesar.py 2025/04/10
+ export PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/private_key.key
+ PRIVATE_KEY_PATH=/home/<USER>/generate/FileSigner/private_key.key
+ export PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/private_key.crt
+ PRIVATE_CRT_PATH=/home/<USER>/generate/FileSigner/private_key.crt
+ export OUTPUT_ROUTE=/home/<USER>/output/excel/
+ OUTPUT_ROUTE=/home/<USER>/output/excel/
+ export OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ OUTPUT_ROUTE_CSV=/home/<USER>/output/csv/
+ valuescsv=("LOG-USUARIOS")
+ export REPORTS_NO_S3=LOG-USUARIOS
+ REPORTS_NO_S3=LOG-USUARIOS
+ ROUTE_CSV=/home/<USER>/output/csv
+ '[' -z 2025/04/11 ']'
+ fecha=2025/04/11
++ date -d '2025/04/11 + 1 day' +%Y%m%d
+ fecha_path=20250412
+ cd /home/<USER>/generate/
+ for value in "${valuescsv[@]}"
+ wait
+ python3 exports_csv/main.py LOG-USUARIOS 2025/04/11
+ cd /home/<USER>/generate/log_usuarios/
+ wait
+ python3 procesar.py 2025/04/11
