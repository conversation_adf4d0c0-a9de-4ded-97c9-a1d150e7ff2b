#!/usr/bin/env python3
"""
🔍 HOMOLOGACIÓN RÁPIDA FINAL
Verificación rápida de homologación con los últimos archivos generados
"""
import pandas as pd
import glob
import os

def homologacion_rapida():
    """Homologación rápida de documentos críticos"""
    print("🔍 HOMOLOGACIÓN RÁPIDA FINAL")
    print("=" * 50)
    
    # Buscar el archivo CSV más reciente
    csv_dir = "/home/<USER>/aws/REP/reports/generate_nv/S3_LOG_USER/output/20250609/csv_exports/csv_final_procesado"
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    
    try:
        archivos_csv = glob.glob(f"{csv_dir}/LOGUSR-FCOMPARTAMOS-*.csv")
        if not archivos_csv:
            print(f"❌ No se encontraron archivos CSV en {csv_dir}")
            return
        
        archivo_mas_reciente = max(archivos_csv, key=os.path.getctime)
        print(f"📄 Archivo más reciente: {os.path.basename(archivo_mas_reciente)}")
        print(f"📄 Oracle: {os.path.basename(archivo_oracle)}")
        
        # Leer archivos
        df_mod = pd.read_csv(archivo_mas_reciente, header=None)
        df_oracle = pd.read_csv(archivo_oracle, header=None)
        
        columnas = ['OPERACION', 'TRANSACTIONID', 'FECHA_HORA', 'CANAL', 'TIPODOCUMENTO', 
                   'DOCUMENTO', 'CELULAR', 'EMPRESA'] + [f'COL{i}' for i in range(8, 30)]
        
        df_mod.columns = columnas[:len(df_mod.columns)]
        df_oracle.columns = columnas[:len(df_oracle.columns)]
        
        print(f"\n📊 ESTADÍSTICAS GENERALES:")
        print(f"   Modernizado: {len(df_mod):,} registros")
        print(f"   Oracle: {len(df_oracle):,} registros")
        
        # Documentos críticos
        documentos_criticos = ["70635331", "76730654", "60918019"]
        
        print(f"\n🔍 VERIFICACIÓN DOCUMENTOS CRÍTICOS:")
        criticos_ok = 0
        
        for documento in documentos_criticos:
            reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
            reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
            
            if len(reg_mod) > 0 and len(reg_oracle) > 0:
                mod_op = reg_mod.iloc[0]['OPERACION']
                oracle_op = reg_oracle.iloc[0]['OPERACION']
                mod_ts = reg_mod.iloc[0]['FECHA_HORA']
                oracle_ts = reg_oracle.iloc[0]['FECHA_HORA']
                
                print(f"   📊 Doc {documento}:")
                print(f"      Modernizado: {mod_op} - {mod_ts}")
                print(f"      Oracle: {oracle_op} - {oracle_ts}")
                
                if mod_op == oracle_op and mod_ts == oracle_ts:
                    print(f"      ✅ HOMOLOGACIÓN PERFECTA")
                    criticos_ok += 1
                elif mod_op == oracle_op:
                    print(f"      ⚠️  OPERACIÓN CORRECTA, timestamp diferente")
                else:
                    print(f"      ❌ OPERACIÓN DIFERENTE")
            else:
                print(f"   ❌ Doc {documento}: No encontrado en uno de los archivos")
        
        # Documentos CPIN vs CCEL
        documentos_ccel = ["03897204", "03877508", "18206061"]
        
        print(f"\n🔍 VERIFICACIÓN DOCUMENTOS CPIN vs CCEL:")
        ccel_ok = 0
        
        for documento in documentos_ccel:
            reg_mod = df_mod[df_mod['DOCUMENTO'].astype(str) == documento]
            reg_oracle = df_oracle[df_oracle['DOCUMENTO'].astype(str) == documento]
            
            if len(reg_mod) > 0 and len(reg_oracle) > 0:
                mod_op = reg_mod.iloc[0]['OPERACION']
                oracle_op = reg_oracle.iloc[0]['OPERACION']
                mod_ts = reg_mod.iloc[0]['FECHA_HORA']
                oracle_ts = reg_oracle.iloc[0]['FECHA_HORA']
                
                print(f"   📊 Doc {documento}:")
                print(f"      Modernizado: {mod_op} - {mod_ts}")
                print(f"      Oracle: {oracle_op} - {oracle_ts}")
                
                if mod_op == oracle_op and mod_ts == oracle_ts:
                    print(f"      ✅ HOMOLOGACIÓN PERFECTA")
                    ccel_ok += 1
                elif mod_op == oracle_op:
                    print(f"      ⚠️  OPERACIÓN CORRECTA, timestamp diferente")
                else:
                    print(f"      ❌ OPERACIÓN DIFERENTE")
            else:
                print(f"   ❌ Doc {documento}: No encontrado en uno de los archivos")
        
        # Resumen final
        total_criticos = len(documentos_criticos)
        total_ccel = len(documentos_ccel)
        
        print(f"\n📊 RESUMEN FINAL:")
        print(f"   🏆 Documentos críticos: {criticos_ok}/{total_criticos} ({criticos_ok/total_criticos*100:.1f}%)")
        print(f"   🔧 Documentos CCEL: {ccel_ok}/{total_ccel} ({ccel_ok/total_ccel*100:.1f}%)")
        
        total_ok = criticos_ok + ccel_ok
        total_docs = total_criticos + total_ccel
        porcentaje_total = (total_ok / total_docs * 100) if total_docs > 0 else 0
        
        print(f"   🎯 TOTAL: {total_ok}/{total_docs} ({porcentaje_total:.1f}%)")
        
        if porcentaje_total == 100:
            print(f"\n🏆 ¡HOMOLOGACIÓN PERFECTA LOGRADA!")
            print(f"✅ Todos los documentos cuadran 'como dos gotas de agua'")
        elif porcentaje_total >= 80:
            print(f"\n✅ EXCELENTE HOMOLOGACIÓN")
            print(f"🔧 Muy cerca del objetivo")
        else:
            print(f"\n⚠️  REQUIERE MÁS AJUSTES")
            print(f"🔧 Continuar con correcciones ninja")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    homologacion_rapida()
