#!/bin/bash

# Script para limpiar espacio en disco
# Creado: $(date)

# Crear directorios de respaldo en /reporting si no existen
mkdir -p /reporting/backup/output
mkdir -p /reporting/backup/logs

# Mover archivos CSV antiguos (más de 30 días) a /reporting
echo "Moviendo archivos CSV antiguos..."
find /home/<USER>/output -type f -name "*.csv" -mtime +30 -exec mv {} /reporting/backup/output/ \;

# Mover archivos de log antiguos (más de 30 días) a /reporting
echo "Moviendo archivos de log antiguos..."
find /home/<USER>/generate/logs -type f -name "*.log" -mtime +30 -exec mv {} /reporting/backup/logs/ \;

# Limpiar archivos temporales
echo "Limpiando archivos temporales..."
rm -rf /tmp/*
rm -rf /var/tmp/*

# Mostrar espacio en disco después de la limpieza
echo "Espacio en disco después de la limpieza:"
df -h /

echo "Limpieza completada."
